/* 
  Localizable.strings
  Solvia

  Created by simon chen on 2025/7/24.
  
*/

// Tab Bar
"tab.observation" = "观察";
"tab.gallery" = "图库";
"tab.log" = "日志";
"tab.profile" = "档案";

// Home Screen
"home.add_device" = "添加你的设备";
"home.my_device" = "我的设备";
"home.add_device_button" = "添加设备";

// Add Device Screen
"add_device.title" = "添加设备";
"add_device.select_device" = "选择你的设备";
"add_device.binoculars" = "Solvia 8x32\nAI 双筒望远镜";
"add_device.monocular" = "Solvia 8x32\nAI 单筒望远镜";
"add_device.next" = "下一步";

// Connection Guide
"connection_guide.title" = "连接设备";
"connection_guide.wifi_reminder" = "开始之前，请确保您手机的Wi-Fi已开启。";
"connection_guide.step1_title" = "激活设备WiFi";
"connection_guide.step1_description" = "打开您的设备电源并激活其WiFi功能。";
"connection_guide.step2_title" = "连接到\"Solvia_xxxx\"";
"connection_guide.step2_description" = "在您的手机上，进入WiFi设置，找到\"Solvia_xxxx\"，并使用密码：12345678进行连接。";
"connection_guide.step3_title" = "返回APP";
"connection_guide.step3_description" = "连接成功后，返回此APP继续操作。";
"connection_guide.go_button" = "开始";

// Permission Requests
"permission.wifi.title" = "需要WiFi访问权限";
"permission.wifi.message" = "为了识别您连接的Solvia设备，我们需要读取当前WiFi网络名称。由于隐私保护，iOS要求位置权限才能访问WiFi信息。我们仅用于设备识别，不会追踪您的位置。";
"permission.photo.title" = "需要相册访问权限";
"permission.photo.message" = "为了在图库中显示您的照片和视频，我们需要访问您的相册。这样您就可以在应用内查看和管理您的媒体内容。";
"permission.allow" = "允许";
"permission.deny" = "暂不";

// Photo Permission Status
"permission.photo.status.authorized" = "相册访问已授权";
"permission.photo.status.limited" = "相册访问受限";
"permission.photo.status.denied" = "相册访问被拒绝";
"permission.photo.status.restricted" = "相册访问受限制";
"permission.photo.status.not_determined" = "需要相册访问权限";
"permission.photo.status.unknown" = "相册访问状态未知";

// Photo Permission Descriptions
"permission.photo.description.authorized" = "您已授权完全访问相册。所有照片和视频都可用。";
"permission.photo.description.limited" = "您已授权有限访问相册。仅选定的照片可用。";
"permission.photo.description.denied" = "相册访问已被拒绝。请在设置中启用以查看您的媒体。";
"permission.photo.description.restricted" = "相册访问受设备策略限制。";
"permission.photo.description.not_determined" = "请授权访问您的相册以查看照片和视频。";
"permission.photo.description.unknown" = "无法确定相册访问状态。";

// Photo Permission Actions
"permission.photo.request" = "授权访问";
"permission.photo.open_settings" = "打开设置";
"permission.photo.denied.title" = "需要相册访问权限";
"permission.photo.denied.message" = "相册访问已被拒绝。要查看您的照片和视频，请在设置中启用相册访问权限。";
"permission.photo.denied.go_settings" = "前往设置";

// Permission Denied
"permission.denied.title" = "需要位置权限";
"permission.denied.message" = "位置权限已被拒绝。为了读取WiFi信息并连接您的Solvia设备，请在设置中开启位置权限。";
"permission.denied.go_settings" = "前往设置";

// Device Connection
"device.connection.checking" = "正在检查WiFi连接...";
"device.connection.success" = "设备连接成功！";
"device.connection.failed" = "请先连接到您的Solvia设备WiFi。";
"device.connection.unknown_device" = "已连接到Solvia设备，但设备类型未知。";

// Device Connected
"device.connected.title" = "设备已连接！";
"device.connected.subtitle" = "您的Solvia设备已成功连接，可以开始使用了。";
"device.connected.type" = "设备类型";
"device.connected.network" = "网络";
"device.connected.unknown" = "未知设备";
"device.connected.continue" = "继续";

// Device Status
"device.status.connected" = "已连接";
"device.status.disconnected" = "未连接";
"device.button.connect" = "连接";
"device.button.enter" = "进入";

// Device Types
"device.type.binoculars" = "AI 双筒望远镜";
"device.type.monocular" = "AI 单筒望远镜";

// Gallery
"gallery.media" = "媒体";
"gallery.albums" = "相册";
"gallery.photos" = "照片";
"gallery.videos" = "视频";
"gallery.favorites" = "收藏";
"gallery.all" = "全部";
"gallery.recents" = "最近";
"gallery.photo" = "照片";
"gallery.video" = "视频";

// Gallery Placeholders
"gallery.media.placeholder" = "媒体库";
"gallery.media.description" = "授权访问相册后，您的照片和视频将显示在这里。";
"gallery.media.loading" = "正在加载媒体库";
"gallery.media.loading.description" = "正在加载您的照片和视频...";
"gallery.albums.placeholder" = "设备相册";
"gallery.albums.description" = "已连接的Solvia设备相册将显示在这里。";

// Gallery Sorting
"gallery.sort.date_newest" = "最新优先";
"gallery.sort.date_oldest" = "最旧优先";
"gallery.sort.name_asc" = "名称A-Z";
"gallery.sort.name_desc" = "名称Z-A";

// Gallery Loading and Actions
"gallery.loading" = "加载中...";
"gallery.retry" = "重试";
"gallery.favorite" = "添加到收藏";
"gallery.unfavorite" = "取消收藏";
"gallery.share" = "分享";
"gallery.delete" = "删除";

// Gallery Sorting
"gallery.sort.title" = "排序方式";

// Gallery Empty States
"gallery.empty.photos.title" = "没有照片";
"gallery.empty.photos.description" = "您拍摄的照片将显示在这里。";
"gallery.empty.videos.title" = "没有视频";
"gallery.empty.videos.description" = "您录制的视频将显示在这里。";
"gallery.empty.favorites.title" = "没有收藏";
"gallery.empty.favorites.description" = "您收藏的照片和视频将显示在这里。";

// Gallery Errors
"gallery.error.no_permission" = "没有相册访问权限";
"gallery.error.loading_failed" = "加载媒体失败";

// Common
"common.cancel" = "取消";
"common.confirm" = "确认";
"common.done" = "完成";
"common.save" = "保存";