//
//  PhotoLibraryManager.swift
//  Solvia
//
//  Created by <PERSON><PERSON> chen on 2025/7/28.
//

import Foundation
import Photos
import SwiftUI

// MARK: - Photo Library Manager
class PhotoLibraryManager: NSObject, ObservableObject {
    static let shared = PhotoLibraryManager()
    
    @Published var authorizationStatus: PHAuthorizationStatus = .notDetermined
    @Published var mediaItems: [MediaItem] = []
    @Published var favoriteItems: Set<String> = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    private let userDefaults = UserDefaults.standard
    private let favoritesKey = "FavoriteMediaItems"
    private let imageManager = PHCachingImageManager()
    
    override init() {
        super.init()
        loadFavorites()
        checkAuthorizationStatus()
    }
    
    // MARK: - Authorization Management
    func checkAuthorizationStatus() {
        if #available(iOS 14, *) {
            authorizationStatus = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        } else {
            authorizationStatus = PHPhotoLibrary.authorizationStatus()
        }
    }

    func requestPhotoLibraryPermission() {
        if #available(iOS 14, *) {
            PHPhotoLibrary.requestAuthorization(for: .readWrite) { [weak self] status in
                DispatchQueue.main.async {
                    self?.authorizationStatus = status
                    if status == .authorized || status == .limited {
                        self?.loadMediaItems()
                    }
                }
            }
        } else {
            PHPhotoLibrary.requestAuthorization { [weak self] status in
                DispatchQueue.main.async {
                    self?.authorizationStatus = status
                    if status == .authorized {
                        self?.loadMediaItems()
                    }
                }
            }
        }
    }

    // 检查是否支持limited访问（iOS 14+）
    private var isLimitedAccessAvailable: Bool {
        if #available(iOS 14, *) {
            return true
        }
        return false
    }
    
    var isAuthorized: Bool {
        return authorizationStatus == .authorized || authorizationStatus == .limited
    }
    
    // MARK: - Media Loading
    func loadMediaItems() {
        guard isAuthorized else {
            DispatchQueue.main.async { [weak self] in
                self?.errorMessage = "gallery.error.no_permission".localized
                self?.isLoading = false
            }
            return
        }
        
        DispatchQueue.main.async { [weak self] in
            self?.isLoading = true
            self?.errorMessage = nil
        }
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            let fetchOptions = PHFetchOptions()
            fetchOptions.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]

            let assets = PHAsset.fetchAssets(with: fetchOptions)
            var items: [MediaItem] = []

            assets.enumerateObjects { asset, _, _ in
                // 只处理图片和视频
                guard asset.mediaType == .image || asset.mediaType == .video else { return }

                let mediaType: MediaItem.MediaType = asset.mediaType == .video ? .video : .photo
                let duration = asset.mediaType == .video ? asset.duration : nil

                let item = MediaItem(
                    type: mediaType,
                    creationDate: asset.creationDate ?? Date(),
                    isFavorite: self.favoriteItems.contains(asset.localIdentifier),
                    localIdentifier: asset.localIdentifier,
                    duration: duration
                )
                items.append(item)
            }

            DispatchQueue.main.async { [weak self] in
                self?.mediaItems = items
                self?.isLoading = false
            }
        }
    }
    
    // MARK: - Media Filtering
    func getPhotos() -> [MediaItem] {
        return mediaItems.filter { $0.type == .photo }
    }
    
    func getVideos() -> [MediaItem] {
        return mediaItems.filter { $0.type == .video }
    }
    
    func getFavorites() -> [MediaItem] {
        return mediaItems.filter { $0.isFavorite }
    }
    
    func getMediaItems(for albumType: MediaAlbum.AlbumType) -> [MediaItem] {
        switch albumType {
        case .all:
            return mediaItems
        case .photos:
            return getPhotos()
        case .videos:
            return getVideos()
        case .favorites:
            return getFavorites()
        case .recents:
            return Array(mediaItems.prefix(100)) // 最近100个项目
        }
    }
    
    // MARK: - Media Grouping
    func groupMediaItemsByDate(_ items: [MediaItem]) -> [MediaSection] {
        let calendar = Calendar.current
        let grouped = Dictionary(grouping: items) { item in
            calendar.startOfDay(for: item.creationDate)
        }
        
        return grouped.map { date, items in
            MediaSection(date: date, items: items.sorted { $0.creationDate > $1.creationDate })
        }.sorted { $0.date > $1.date }
    }
    
    // MARK: - Sorting
    func sortMediaItems(_ items: [MediaItem], by sortOption: MediaSortOption) -> [MediaItem] {
        switch sortOption {
        case .dateDescending:
            return items.sorted { $0.creationDate > $1.creationDate }
        case .dateAscending:
            return items.sorted { $0.creationDate < $1.creationDate }
        case .nameAscending:
            return items.sorted { $0.localIdentifier < $1.localIdentifier }
        case .nameDescending:
            return items.sorted { $0.localIdentifier > $1.localIdentifier }
        }
    }
    
    // MARK: - Favorites Management
    func toggleFavorite(for item: MediaItem) {
        if favoriteItems.contains(item.localIdentifier) {
            favoriteItems.remove(item.localIdentifier)
        } else {
            favoriteItems.insert(item.localIdentifier)
        }
        
        // 更新mediaItems中的收藏状态
        if let index = mediaItems.firstIndex(where: { $0.id == item.id }) {
            mediaItems[index].isFavorite = favoriteItems.contains(item.localIdentifier)
        }
        
        saveFavorites()
    }
    
    func isFavorite(_ item: MediaItem) -> Bool {
        return favoriteItems.contains(item.localIdentifier)
    }
    
    private func saveFavorites() {
        userDefaults.set(Array(favoriteItems), forKey: favoritesKey)
    }
    
    private func loadFavorites() {
        if let favorites = userDefaults.array(forKey: favoritesKey) as? [String] {
            favoriteItems = Set(favorites)
        }
    }
    
    // MARK: - Image Loading
    func loadThumbnail(for item: MediaItem, targetSize: CGSize, completion: @escaping (UIImage?) -> Void) {
        guard let asset = PHAsset.fetchAssets(withLocalIdentifiers: [item.localIdentifier], options: nil).firstObject else {
            DispatchQueue.main.async {
                completion(nil)
            }
            return
        }
        
        let options = PHImageRequestOptions()
        options.deliveryMode = .fastFormat
        options.resizeMode = .fast
        options.isNetworkAccessAllowed = true
        options.isSynchronous = false
        
        imageManager.requestImage(for: asset, targetSize: targetSize, contentMode: .aspectFill, options: options) { image, info in
            DispatchQueue.main.async {
                // 检查是否有错误
                if let error = info?[PHImageErrorKey] as? Error {
                    print("Error loading thumbnail: \(error)")
                    completion(nil)
                } else {
                    completion(image)
                }
            }
        }
    }
    
    func loadFullImage(for item: MediaItem, completion: @escaping (UIImage?) -> Void) {
        guard let asset = PHAsset.fetchAssets(withLocalIdentifiers: [item.localIdentifier], options: nil).firstObject else {
            DispatchQueue.main.async {
                completion(nil)
            }
            return
        }
        
        let options = PHImageRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.isNetworkAccessAllowed = true
        options.isSynchronous = false
        
        imageManager.requestImage(for: asset, targetSize: PHImageManagerMaximumSize, contentMode: .aspectFit, options: options) { image, info in
            DispatchQueue.main.async {
                // 检查是否有错误
                if let error = info?[PHImageErrorKey] as? Error {
                    print("Error loading full image: \(error)")
                    completion(nil)
                } else {
                    completion(image)
                }
            }
        }
    }
    
    // MARK: - Statistics
    func getMediaStatistics() -> (totalCount: Int, photoCount: Int, videoCount: Int, favoriteCount: Int) {
        let totalCount = mediaItems.count
        let photoCount = getPhotos().count
        let videoCount = getVideos().count
        let favoriteCount = getFavorites().count
        
        return (totalCount, photoCount, videoCount, favoriteCount)
    }
    
    // MARK: - Error Handling
    func clearError() {
        errorMessage = nil
    }
    
    // MARK: - Refresh
    func refreshMediaLibrary() {
        guard isAuthorized else {
            requestPhotoLibraryPermission()
            return
        }
        
        loadMediaItems()
    }
}