//
//  WiFiManager.swift
//  Solvia
//
//  Created by si<PERSON> chen on 2025/7/25.
//

import Foundation
import CoreLocation
import UIKit

class WiFiManager: NSObject, ObservableObject {
    static let shared = WiFiManager()
    
    @Published var currentSSID: String?
    @Published var isLocationPermissionGranted = false
    
    private let locationManager = CLLocationManager()
    
    // MARK: - WiFi信息获取说明
    /*
     为什么需要位置权限来读取WiFi信息？
     
     从iOS 13开始，苹果将WiFi网络信息归类为位置相关数据，因为：
     1. WiFi热点可以用来推断用户的大致位置
     2. 这是苹果隐私保护策略的一部分
     3. 防止恶意应用通过WiFi信息追踪用户
     
     在生产环境中实现WiFi SSID获取需要：
     1. 导入 SystemConfiguration 框架
     2. 使用 CNCopySupportedInterfaces() 和 CNCopyCurrentNetworkInfo()
     3. 在Info.plist中添加 NSLocation<PERSON>henInUseUsageDescription
     4. 在entitlements中添加 com.apple.developer.networking.wifi-info
     5. 请求并获得位置权限（仅用于WiFi信息读取）
     
     示例代码：
     import SystemConfiguration.CaptiveNetwork
     
     guard let interfaces = CNCopySupportedInterfaces() as? [String] else { return nil }
     for interface in interfaces {
         if let networkInfo = CNCopyCurrentNetworkInfo(interface as CFString) as? [String: Any],
            let ssid = networkInfo[kCNNetworkInfoKeySSID as String] as? String {
             return ssid
         }
     }
     */
    
    override init() {
        super.init()
        locationManager.delegate = self
        checkLocationPermissionStatus()
    }
    
    // MARK: - Permission Management
    
    /// 检查位置权限状态
    private func checkLocationPermissionStatus() {
        switch locationManager.authorizationStatus {
        case .authorizedWhenInUse, .authorizedAlways:
            isLocationPermissionGranted = true
        case .denied, .restricted:
            isLocationPermissionGranted = false
        case .notDetermined:
            isLocationPermissionGranted = false
        @unknown default:
            isLocationPermissionGranted = false
        }
    }
    
    /// 获取当前权限状态
    func getLocationPermissionStatus() -> CLAuthorizationStatus {
        return locationManager.authorizationStatus
    }
    
    /// 检查权限是否被拒绝
    func isLocationPermissionDenied() -> Bool {
        return locationManager.authorizationStatus == .denied || locationManager.authorizationStatus == .restricted
    }
    
    /// 请求位置权限
    func requestLocationPermission() {
        print("Requesting location permission, current status: \(locationManager.authorizationStatus.rawValue)")
        locationManager.requestWhenInUseAuthorization()
    }
    
    // MARK: - WiFi Operations
    
    /// 跳转到系统WiFi设置页面
    func openWiFiSettings() {
        DispatchQueue.main.async {
            // 尝试多种WiFi设置的URL方案
            let wifiURLs = [
                "prefs:root=WIFI",
                "App-Prefs:root=WIFI", 
                "App-prefs:WIFI",
                "prefs:WIFI"
            ]
            
            var opened = false
            
            // 尝试各种WiFi设置URL
            for urlString in wifiURLs {
                if let url = URL(string: urlString) {
                    if UIApplication.shared.canOpenURL(url) {
                        UIApplication.shared.open(url, options: [:]) { success in
                            if success {
                                print("Successfully opened WiFi settings with: \(urlString)")
                            }
                        }
                        opened = true
                        break
                    }
                }
            }
            
            // 如果所有WiFi深度链接都不可用，回退到通用设置页面
            if !opened {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl, options: [:]) { success in
                        if success {
                            print("Opened general settings as fallback - user can navigate to WiFi manually")
                        } else {
                            print("Failed to open any settings")
                        }
                    }
                }
            }
        }
    }
    
    /// 跳转到应用设置页面（用于权限管理）
    func openAppSettings() {
        DispatchQueue.main.async {
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsUrl, options: [:]) { success in
                    if !success {
                        print("Failed to open app settings")
                    }
                }
            }
        }
    }
    
    /// 获取当前连接的WiFi SSID
    func getCurrentWiFiSSID() -> String? {
        guard isLocationPermissionGranted else {
            print("Location permission not granted, cannot read WiFi SSID")
            // 为了测试目的，返回一个模拟的SSID
            #if DEBUG
            return "Solvia_Binocular_Test"
            #else
            return nil
            #endif
        }
        
        // 在实际项目中，这里会调用系统API获取WiFi信息
        // 由于SystemConfiguration框架的复杂性，这里先使用模拟数据
        
        // 模拟不同的WiFi连接状态
        let simulatedSSIDs = [
            "Solvia_Binocular_001",
            "Solvia_Monocular_002", 
            "Solvia_Unknown_003",
            "MyHomeWiFi"
        ]
        
        // 在DEBUG模式下返回模拟的Solvia设备SSID
        #if DEBUG
        let randomSSID = simulatedSSIDs.randomElement() ?? "Solvia_Binocular_Test"
        DispatchQueue.main.async {
            self.currentSSID = randomSSID
        }
        return randomSSID
        #else
        // 在生产环境中，这里应该实现真正的WiFi SSID获取逻辑
        // 需要使用SystemConfiguration框架和CaptiveNetwork API
        print("WiFi SSID reading not implemented for production")
        return nil
        #endif
    }
    
    /// 检查是否连接到Solvia设备
    func isConnectedToSolviaDevice() -> Bool {
        // 首先尝试获取最新的SSID
        let ssid = getCurrentWiFiSSID() ?? currentSSID
        
        guard let currentSSID = ssid else {
            return false
        }
        
        // 检查SSID是否以"Solvia"开头
        return currentSSID.lowercased().hasPrefix("solvia")
    }
    
    /// 从SSID判断设备类型
    func getDeviceTypeFromSSID() -> String? {
        // 首先尝试获取最新的SSID
        let ssid = getCurrentWiFiSSID() ?? currentSSID
        
        guard let currentSSID = ssid else {
            return nil
        }
        
        let lowercaseSSID = currentSSID.lowercased()
        
        if lowercaseSSID.contains("binocular") {
            return "binoculars"
        } else if lowercaseSSID.contains("monocular") {
            return "monocular"
        } else if lowercaseSSID.hasPrefix("solvia") {
            // 如果只是以Solvia开头但没有具体类型，返回通用类型
            return "unknown_solvia_device"
        }
        
        return nil
    }
}

// MARK: - CLLocationManagerDelegate
extension WiFiManager: CLLocationManagerDelegate {
    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        print("Location authorization changed to: \(manager.authorizationStatus.rawValue)")
        DispatchQueue.main.async {
            self.checkLocationPermissionStatus()
        }
    }
}