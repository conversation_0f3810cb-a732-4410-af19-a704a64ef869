//
//  LocalizationManager.swift
//  Solvia
//
//  Created by si<PERSON> chen on 2025/7/25.
//

import Foundation
import SwiftUI

/// 本地化管理器，用于处理语言切换和通知
class LocalizationManager: ObservableObject {
    static let shared = LocalizationManager()
    
    @Published var currentLanguage: String = Locale.current.languageCode ?? "en"
    
    private init() {
        // 监听系统语言变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(languageDidChange),
            name: NSLocale.currentLocaleDidChangeNotification,
            object: nil
        )
    }
    
    @objc private func languageDidChange() {
        DispatchQueue.main.async {
            self.currentLanguage = Locale.current.languageCode ?? "en"
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

/// 本地化字符串视图修饰符
struct LocalizedStringKey: ViewModifier {
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    func body(content: Content) -> some View {
        content
            .id(localizationManager.currentLanguage) // 强制重新渲染
    }
}

extension View {
    /// 添加本地化支持，当语言变化时自动更新视图
    func localized() -> some View {
        self.modifier(LocalizedStringKey())
    }
}