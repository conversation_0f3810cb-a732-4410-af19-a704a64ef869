<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <!-- 主Logo区域 -->
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="logo-container">
                                <rect key="frame" x="66.5" y="371" width="260" height="110"/>
                                <subviews>
                                    <!-- Logo背景 - 简化版本，不使用运行时属性 -->
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="logo-background">
                                        <rect key="frame" x="0.0" y="0.0" width="260" height="110"/>
                                        <color key="backgroundColor" red="0.45" green="0.65" blue="0.25" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </view>
                                    
                                    <!-- MATATA 文字 -->
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="MATATA" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="matata-label">
                                        <rect key="frame" x="0.0" y="25" width="260" height="36"/>
                                        <fontDescription key="fontDescription" type="system" weight="black" pointSize="36"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </label>
                                    
                                    <!-- XPLORE 文字 -->
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="XPLORE" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xplore-label">
                                        <rect key="frame" x="0.0" y="57" width="260" height="36"/>
                                        <fontDescription key="fontDescription" type="system" weight="black" pointSize="36"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </label>
                                </subviews>
                                <constraints>
                                    <!-- Logo背景约束 -->
                                    <constraint firstItem="logo-background" firstAttribute="leading" secondItem="logo-container" secondAttribute="leading" id="logo-bg-leading"/>
                                    <constraint firstItem="logo-background" firstAttribute="trailing" secondItem="logo-container" secondAttribute="trailing" id="logo-bg-trailing"/>
                                    <constraint firstItem="logo-background" firstAttribute="top" secondItem="logo-container" secondAttribute="top" id="logo-bg-top"/>
                                    <constraint firstItem="logo-background" firstAttribute="bottom" secondItem="logo-container" secondAttribute="bottom" id="logo-bg-bottom"/>
                                    
                                    <!-- MATATA文字约束 -->
                                    <constraint firstItem="matata-label" firstAttribute="leading" secondItem="logo-container" secondAttribute="leading" id="matata-leading"/>
                                    <constraint firstItem="matata-label" firstAttribute="trailing" secondItem="logo-container" secondAttribute="trailing" id="matata-trailing"/>
                                    <constraint firstItem="matata-label" firstAttribute="top" secondItem="logo-container" secondAttribute="top" constant="25" id="matata-top"/>
                                    <constraint firstItem="matata-label" firstAttribute="height" constant="36" id="matata-height"/>
                                    
                                    <!-- XPLORE文字约束 -->
                                    <constraint firstItem="xplore-label" firstAttribute="leading" secondItem="logo-container" secondAttribute="leading" id="xplore-leading"/>
                                    <constraint firstItem="xplore-label" firstAttribute="trailing" secondItem="logo-container" secondAttribute="trailing" id="xplore-trailing"/>
                                    <constraint firstItem="xplore-label" firstAttribute="top" secondItem="matata-label" secondAttribute="bottom" constant="-4" id="xplore-top"/>
                                    <constraint firstItem="xplore-label" firstAttribute="height" constant="36" id="xplore-height"/>
                                </constraints>
                                <constraints>
                                    <constraint firstAttribute="width" constant="260" id="logo-width"/>
                                    <constraint firstAttribute="height" constant="110" id="logo-height"/>
                                </constraints>
                            </view>
                            
                            <!-- 底部 Power by MatataStudio 文字 - 单行显示 -->
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Power by MatataStudio" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bottom-text-label">
                                <rect key="frame" x="0.0" y="772" width="393" height="13"/>
                                <fontDescription key="fontDescription" type="system" weight="light" pointSize="13"/>
                                <color key="textColor" red="1" green="1" blue="1" alpha="0.7" colorSpace="custom" customColorSpace="sRGB"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" red="0.08" green="0.08" blue="0.08" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <!-- Logo居中约束 -->
                            <constraint firstItem="logo-container" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="logo-center-x"/>
                            <constraint firstItem="logo-container" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="logo-center-y"/>
                            
                            <!-- 底部文字约束 -->
                            <constraint firstItem="bottom-text-label" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="bottom-leading"/>
                            <constraint firstItem="bottom-text-label" firstAttribute="trailing" secondItem="Ze5-6b-2t3" secondAttribute="trailing" id="bottom-trailing"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="bottom" secondItem="bottom-text-label" secondAttribute="bottom" constant="50" id="bottom-bottom"/>
                            <constraint firstItem="bottom-text-label" firstAttribute="height" constant="13" id="bottom-height"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="53" y="375"/>
        </scene>
    </scenes>
</document>