//
//  String+Localization.swift
//  Solvia
//
//  Created by <PERSON><PERSON> chen on 2025/7/24.
//

import Foundation
import SwiftUI

extension String {
    /// 本地化字符串
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
    
    /// 带参数的本地化字符串
    func localized(with arguments: CVarArg...) -> String {
        return String(format: NSLocalizedString(self, comment: ""), arguments: arguments)
    }
}

/// SwiftUI本地化文本视图
struct LocalizedText: View {
    private let key: String
    private let arguments: [CVarArg]
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    init(_ key: String, arguments: CVarArg...) {
        self.key = key
        self.arguments = arguments
    }
    
    var body: some View {
        if arguments.isEmpty {
            Text(key.localized)
                .id(localizationManager.currentLanguage)
        } else {
            Text(key.localized(with: arguments))
                .id(localizationManager.currentLanguage)
        }
    }
}