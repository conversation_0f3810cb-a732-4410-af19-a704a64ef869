//
//  GalleryView.swift
//  Solvia
//
//  Created by si<PERSON> chen on 2025/7/28.
//

import SwiftUI

struct GalleryView: View {
    @State private var selectedTab: GalleryTab = .media
    @ObservedObject private var localizationManager = LocalizationManager.shared
    @ObservedObject private var photoLibraryManager = PhotoLibraryManager.shared
    
    enum GalleryTab: CaseIterable {
        case media
        case albums
        
        var displayName: String {
            switch self {
            case .media:
                return "gallery.media".localized
            case .albums:
                return "gallery.albums".localized
            }
        }
    }
    
    var body: some View {
        ZStack {
            // 深色背景
            Color(red: 0.08, green: 0.08, blue: 0.08)
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 顶部标签切换区域 - 居中显示
                HStack {
                    Spacer()

                    HStack(spacing: 32) {
                        ForEach(GalleryTab.allCases, id: \.self) { tab in
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    selectedTab = tab
                                }
                            }) {
                                LocalizedText(tab == .media ? "gallery.media" : "gallery.albums")
                                    .font(.system(size: 20, weight: .medium))
                                    .foregroundColor(selectedTab == tab ? .white : Color(red: 0.6, green: 0.6, blue: 0.6))
                            }
                        }
                    }

                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
                .padding(.bottom, 24)
                
                // 内容区域
                TabView(selection: $selectedTab) {
                    // Media标签页内容
                    MediaTabContentView()
                        .tag(GalleryTab.media)
                    
                    // Albums标签页内容
                    AlbumsTabPlaceholderView()
                        .tag(GalleryTab.albums)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            photoLibraryManager.checkAuthorizationStatus()
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
            // 当应用从后台返回前台时，重新检查权限状态
            photoLibraryManager.checkAuthorizationStatus()
        }
        .id(localizationManager.currentLanguage)
    }
}

// MARK: - Media Tab Content View
struct MediaTabContentView: View {
    @ObservedObject private var photoLibraryManager = PhotoLibraryManager.shared
    @State private var hasRequestedPermission = false

    var body: some View {
        Group {
            if photoLibraryManager.isAuthorized {
                // 已授权，显示媒体内容
                MediaTabView()
                    .transition(.opacity)
            } else {
                // 未授权，显示权限请求界面
                PhotoPermissionStatusView()
                    .transition(.opacity)
            }
        }
        .animation(.easeInOut(duration: 0.15), value: photoLibraryManager.isAuthorized)
        .onChange(of: photoLibraryManager.authorizationStatus) { status in
            // 当权限状态变为已授权时，立即切换到媒体界面
            if status == .authorized || status == .limited {
                hasRequestedPermission = true
            }
        }
    }
}

struct AlbumsTabPlaceholderView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "folder.badge.gearshape")
                .font(.system(size: 60))
                .foregroundColor(Color(red: 0.4, green: 0.4, blue: 0.4))
            
            LocalizedText("gallery.albums.placeholder")
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.white)
            
            LocalizedText("gallery.albums.description")
                .font(.system(size: 14))
                .foregroundColor(Color(red: 0.7, green: 0.7, blue: 0.7))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(red: 0.08, green: 0.08, blue: 0.08))
    }
}

#Preview {
    GalleryView()
}