//
//  PhotoPermissionRequestView.swift
//  Solvia
//
//  Created by <PERSON><PERSON> chen on 2025/7/28.
//

import SwiftUI
import Photos

struct PhotoPermissionRequestView: View {
    let onAllow: () -> Void
    let onDeny: () -> Void
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    var body: some View {
        PermissionRequestView(
            titleKey: "permission.photo.title",
            messageKey: "permission.photo.message",
            onAllow: onAllow,
            onDeny: onDeny
        )
    }
}

// MARK: - Photo Permission Denied View
struct PhotoPermissionDeniedView: View {
    let onOpenSettings: () -> Void
    let onCancel: () -> Void
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.5)
                .ignoresSafeArea()
            
            // 弹框内容
            VStack(spacing: 20) {
                // 图标
                Image(systemName: "photo.badge.exclamationmark.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.orange)
                
                // 标题
                LocalizedText("permission.photo.denied.title")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                
                // 消息
                LocalizedText("permission.photo.denied.message")
                    .font(.system(size: 14))
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)
                    .padding(.horizontal, 20)
                
                // 按钮
                HStack(spacing: 12) {
                    // 取消按钮
                    Button(action: onCancel) {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(red: 0.3, green: 0.3, blue: 0.3))
                            .frame(height: 44)
                            .overlay(
                                LocalizedText("common.cancel")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                            )
                    }
                    
                    // 去设置按钮
                    Button(action: onOpenSettings) {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(red: 0.45, green: 0.65, blue: 0.25))
                            .frame(height: 44)
                            .overlay(
                                LocalizedText("permission.photo.denied.go_settings")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                            )
                    }
                }
                .padding(.horizontal, 20)
            }
            .padding(.all, 24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(red: 0.15, green: 0.15, blue: 0.15))
            )
            .padding(.horizontal, 40)
        }
        .id(localizationManager.currentLanguage)
    }
}

// MARK: - Photo Permission Status View
struct PhotoPermissionStatusView: View {
    @ObservedObject private var photoLibraryManager = PhotoLibraryManager.shared
    @State private var showingPermissionRequest = false
    @State private var showingPermissionDenied = false
    @State private var isRequestingPermission = false
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    var body: some View {
        VStack(spacing: 24) {
            // 权限状态图标
            Image(systemName: permissionStatusIcon)
                .font(.system(size: 60))
                .foregroundColor(permissionStatusColor)
            
            // 权限状态标题
            LocalizedText(permissionStatusTitle)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            
            // 权限状态描述
            LocalizedText(permissionStatusDescription)
                .font(.system(size: 14))
                .foregroundColor(Color(red: 0.7, green: 0.7, blue: 0.7))
                .multilineTextAlignment(.center)
                .lineLimit(nil)
                .padding(.horizontal, 40)
            
            // 操作按钮
            if photoLibraryManager.authorizationStatus == .notDetermined {
                Button(action: {
                    showingPermissionRequest = true
                }) {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(red: 0.45, green: 0.65, blue: 0.25))
                        .frame(height: 50)
                        .frame(maxWidth: 200)
                        .overlay(
                            LocalizedText("permission.photo.request")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                        )
                }
            } else if photoLibraryManager.authorizationStatus == .denied {
                Button(action: {
                    showingPermissionDenied = true
                }) {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(red: 0.45, green: 0.65, blue: 0.25))
                        .frame(height: 50)
                        .frame(maxWidth: 200)
                        .overlay(
                            LocalizedText("permission.photo.open_settings")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                        )
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(red: 0.08, green: 0.08, blue: 0.08))
        .overlay(
            // 权限请求弹框
            Group {
                if showingPermissionRequest {
                    PhotoPermissionRequestView(
                        onAllow: {
                            showingPermissionRequest = false
                            photoLibraryManager.requestPhotoLibraryPermission()
                        },
                        onDeny: {
                            showingPermissionRequest = false
                        }
                    )
                    .transition(.opacity)
                }
            }
        )
        .overlay(
            // 权限被拒绝弹框
            Group {
                if showingPermissionDenied {
                    PhotoPermissionDeniedView(
                        onOpenSettings: {
                            showingPermissionDenied = false
                            openAppSettings()
                        },
                        onCancel: {
                            showingPermissionDenied = false
                        }
                    )
                    .transition(.opacity)
                }
            }
        )
        .onAppear {
            photoLibraryManager.checkAuthorizationStatus()
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
            // 当应用从后台返回前台时，重新检查权限状态
            photoLibraryManager.checkAuthorizationStatus()
        }
        .id(localizationManager.currentLanguage)
    }
    
    private var permissionStatusIcon: String {
        switch photoLibraryManager.authorizationStatus {
        case .authorized, .limited:
            return "checkmark.circle.fill"
        case .denied, .restricted:
            return "xmark.circle.fill"
        case .notDetermined:
            return "questionmark.circle.fill"
        @unknown default:
            return "exclamationmark.circle.fill"
        }
    }
    
    private var permissionStatusColor: Color {
        switch photoLibraryManager.authorizationStatus {
        case .authorized, .limited:
            return .green
        case .denied, .restricted:
            return .red
        case .notDetermined:
            return .orange
        @unknown default:
            return .gray
        }
    }
    
    private var permissionStatusTitle: String {
        switch photoLibraryManager.authorizationStatus {
        case .authorized:
            return "permission.photo.status.authorized"
        case .limited:
            return "permission.photo.status.limited"
        case .denied:
            return "permission.photo.status.denied"
        case .restricted:
            return "permission.photo.status.restricted"
        case .notDetermined:
            return "permission.photo.status.not_determined"
        @unknown default:
            return "permission.photo.status.unknown"
        }
    }
    
    private var permissionStatusDescription: String {
        switch photoLibraryManager.authorizationStatus {
        case .authorized:
            return "permission.photo.description.authorized"
        case .limited:
            return "permission.photo.description.limited"
        case .denied:
            return "permission.photo.description.denied"
        case .restricted:
            return "permission.photo.description.restricted"
        case .notDetermined:
            return "permission.photo.description.not_determined"
        @unknown default:
            return "permission.photo.description.unknown"
        }
    }
    
    private func openAppSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
}

#Preview {
    PhotoPermissionStatusView()
}