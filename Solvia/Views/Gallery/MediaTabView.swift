//
//  MediaTabView.swift
//  Solvia
//
//  Created by <PERSON><PERSON> chen on 2025/7/28.
//

import SwiftUI
import Photos

struct MediaTabView: View {
    @State private var selectedMediaType: MediaType = .photos
    @State private var sortOption: MediaSortOption = .dateDescending
    @State private var showingSortMenu = false
    @ObservedObject private var photoLibraryManager = PhotoLibraryManager.shared
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    enum MediaType: CaseIterable {
        case photos
        case videos
        case favorites
        
        var displayName: String {
            switch self {
            case .photos:
                return "gallery.photos".localized
            case .videos:
                return "gallery.videos".localized
            case .favorites:
                return "gallery.favorites".localized
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 子标签和排序按钮
            HStack {
                // Photos/Videos/Favorites 子标签
                HStack(spacing: 6) {
                    ForEach(MediaType.allCases, id: \.self) { type in
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                selectedMediaType = type
                            }
                        }) {
                            RoundedRectangle(cornerRadius: 18)
                                .fill(selectedMediaType == type ? Color(red: 0.25, green: 0.25, blue: 0.25) : Color.clear)
                                .frame(height: 36)
                                .overlay(
                                    LocalizedText(type.displayName)
                                        .font(.system(size: 15, weight: selectedMediaType == type ? .semibold : .medium))
                                        .foregroundColor(selectedMediaType == type ? .white : Color(red: 0.65, green: 0.65, blue: 0.65))
                                )
                                .padding(.horizontal, 18)
                        }
                    }
                }
                
                Spacer()
                
                // 排序按钮
                Button(action: {
                    showingSortMenu = true
                }) {
                    Image(systemName: "chevron.down")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 32, height: 32)
                        .background(
                            Circle()
                                .fill(Color(red: 0.2, green: 0.2, blue: 0.2))
                        )
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 16)
            
            // 媒体内容区域
            if photoLibraryManager.isLoading {
                LoadingView()
            } else if let errorMessage = photoLibraryManager.errorMessage {
                ErrorView(message: errorMessage) {
                    photoLibraryManager.clearError()
                    photoLibraryManager.refreshMediaLibrary()
                }
            } else {
                MediaGridView(
                    mediaType: selectedMediaType,
                    sortOption: sortOption
                )
            }
        }
        .actionSheet(isPresented: $showingSortMenu) {
            ActionSheet(
                title: Text("gallery.sort.title".localized),
                buttons: MediaSortOption.allCases.map { option in
                    .default(Text(option.displayName)) {
                        sortOption = option
                    }
                } + [.cancel()]
            )
        }
        .onAppear {
            if photoLibraryManager.isAuthorized && photoLibraryManager.mediaItems.isEmpty {
                photoLibraryManager.loadMediaItems()
            }
        }
        .id(localizationManager.currentLanguage)
    }
}

// MARK: - Loading View
struct LoadingView: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.2)
            
            LocalizedText("gallery.loading")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(red: 0.08, green: 0.08, blue: 0.08))
    }
}

// MARK: - Error View
struct ErrorView: View {
    let message: String
    let onRetry: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 50))
                .foregroundColor(.orange)
            
            Text(message)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
            
            Button(action: onRetry) {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(red: 0.45, green: 0.65, blue: 0.25))
                    .frame(height: 44)
                    .frame(maxWidth: 200)
                    .overlay(
                        LocalizedText("gallery.retry")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                    )
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(red: 0.08, green: 0.08, blue: 0.08))
    }
}

#Preview {
    MediaTabView()
}