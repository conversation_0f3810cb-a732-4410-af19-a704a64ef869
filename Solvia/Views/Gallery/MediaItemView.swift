//
//  MediaItemView.swift
//  Solvia
//
//  Created by <PERSON><PERSON> chen on 2025/7/28.
//

import SwiftUI
import Photos

struct MediaItemView: View {
    let item: MediaItem
    let onTap: () -> Void
    let onFavoriteToggle: () -> Void
    
    @State private var thumbnail: UIImage?
    @State private var isLoading = true
    @ObservedObject private var photoLibraryManager = PhotoLibraryManager.shared
    
    var body: some View {
        Button(action: onTap) {
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color(red: 0.15, green: 0.15, blue: 0.15))
                
                // 缩略图
                if let thumbnail = thumbnail {
                    Image(uiImage: thumbnail)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .clipped()
                        .cornerRadius(4)
                } else if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    // 加载失败的占位图
                    Image(systemName: item.type == .video ? "video.slash" : "photo")
                        .font(.system(size: 24))
                        .foregroundColor(Color(red: 0.5, green: 0.5, blue: 0.5))
                }
                
                // 视频时长标识
                if item.type == .video, let duration = item.duration {
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Text(formatDuration(duration))
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(Color.black.opacity(0.7))
                                )
                                .padding(.trailing, 4)
                                .padding(.bottom, 4)
                        }
                    }
                }
                
                // 收藏标识
                if item.isFavorite {
                    VStack {
                        HStack {
                            Spacer()
                            Image(systemName: "heart.fill")
                                .font(.system(size: 14))
                                .foregroundColor(.red)
                                .padding(.top, 4)
                                .padding(.trailing, 4)
                        }
                        Spacer()
                    }
                }
                
                // 收藏按钮（长按显示）
                VStack {
                    HStack {
                        Button(action: onFavoriteToggle) {
                            Image(systemName: item.isFavorite ? "heart.fill" : "heart")
                                .font(.system(size: 16))
                                .foregroundColor(item.isFavorite ? .red : .white)
                                .frame(width: 32, height: 32)
                                .background(
                                    Circle()
                                        .fill(Color.black.opacity(0.5))
                                )
                        }
                        .opacity(0) // 默认隐藏，可以通过手势显示
                        .padding(.top, 4)
                        .padding(.leading, 4)
                        
                        Spacer()
                    }
                    Spacer()
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .onAppear {
            loadThumbnail()
        }
        .contextMenu {
            // 长按菜单
            Button(action: onFavoriteToggle) {
                Label(
                    item.isFavorite ? "gallery.unfavorite".localized : "gallery.favorite".localized,
                    systemImage: item.isFavorite ? "heart.slash" : "heart"
                )
            }
            
            Button(action: {
                // TODO: 分享功能
            }) {
                Label("gallery.share".localized, systemImage: "square.and.arrow.up")
            }
            
            Button(action: {
                // TODO: 删除功能
            }) {
                Label("gallery.delete".localized, systemImage: "trash")
            }
        }
    }
    
    private func loadThumbnail() {
        isLoading = true
        let targetSize = CGSize(width: 200, height: 200) // 适合网格显示的缩略图大小
        
        photoLibraryManager.loadThumbnail(for: item, targetSize: targetSize) { loadedImage in
            self.thumbnail = loadedImage
            self.isLoading = false
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

#Preview {
    let sampleItem = MediaItem(
        type: .photo,
        creationDate: Date(),
        isFavorite: false,
        localIdentifier: "sample"
    )
    
    return MediaItemView(
        item: sampleItem,
        onTap: {},
        onFavoriteToggle: {}
    )
    .frame(width: 100, height: 100)
    .background(Color.black)
}