//
//  MediaGridView.swift
//  Solvia
//
//  Created by <PERSON><PERSON> chen on 2025/7/28.
//

import SwiftUI
import Photos

struct MediaGridView: View {
    let mediaType: MediaTabView.MediaType
    let sortOption: MediaSortOption
    
    @ObservedObject private var photoLibraryManager = PhotoLibraryManager.shared
    @ObservedObject private var localizationManager = LocalizationManager.shared
    @State private var selectedItem: MediaItem?
    
    private let columns = [
        GridItem(.flexible(), spacing: 2),
        GridItem(.flexible(), spacing: 2),
        GridItem(.flexible(), spacing: 2),
        GridItem(.flexible(), spacing: 2)
    ]
    
    private var filteredItems: [MediaItem] {
        let items = photoLibraryManager.getMediaItems(for: albumType)
        return photoLibraryManager.sortMediaItems(items, by: sortOption)
    }
    
    private var albumType: MediaAlbum.AlbumType {
        switch mediaType {
        case .photos:
            return .photos
        case .videos:
            return .videos
        case .favorites:
            return .favorites
        }
    }
    
    private var groupedSections: [MediaSection] {
        return photoLibraryManager.groupMediaItemsByDate(filteredItems)
    }
    
    var body: some View {
        Group {
            if filteredItems.isEmpty {
                EmptyStateView(mediaType: mediaType)
            } else {
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 16) {
                        ForEach(groupedSections) { section in
                            VStack(alignment: .leading, spacing: 8) {
                                // 日期标题
                                HStack {
                                    Text(section.formattedDate)
                                        .font(.system(size: 16, weight: .semibold))
                                        .foregroundColor(.white)
                                    
                                    Text("(\(section.itemCount))")
                                        .font(.system(size: 14))
                                        .foregroundColor(Color(red: 0.7, green: 0.7, blue: 0.7))
                                    
                                    Spacer()
                                }
                                .padding(.horizontal, 20)
                                
                                // 媒体网格
                                LazyVGrid(columns: columns, spacing: 2) {
                                    ForEach(section.items) { item in
                                        MediaItemView(
                                            item: item,
                                            onTap: {
                                                selectedItem = item
                                            },
                                            onFavoriteToggle: {
                                                photoLibraryManager.toggleFavorite(for: item)
                                            }
                                        )
                                        .aspectRatio(1, contentMode: .fit)
                                    }
                                }
                                .padding(.horizontal, 20)
                            }
                        }
                    }
                    .padding(.vertical, 16)
                }
            }
        }
        .fullScreenCover(item: $selectedItem) { item in
            MediaDetailView(item: item)
        }
        .id(localizationManager.currentLanguage)
    }
}

// MARK: - Empty State View
struct EmptyStateView: View {
    let mediaType: MediaTabView.MediaType
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    private var emptyStateConfig: (icon: String, title: String, description: String) {
        switch mediaType {
        case .photos:
            return ("photo", "gallery.empty.photos.title", "gallery.empty.photos.description")
        case .videos:
            return ("video", "gallery.empty.videos.title", "gallery.empty.videos.description")
        case .favorites:
            return ("heart", "gallery.empty.favorites.title", "gallery.empty.favorites.description")
        }
    }
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: emptyStateConfig.icon)
                .font(.system(size: 60))
                .foregroundColor(Color(red: 0.4, green: 0.4, blue: 0.4))
            
            LocalizedText(emptyStateConfig.title)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.white)
            
            LocalizedText(emptyStateConfig.description)
                .font(.system(size: 14))
                .foregroundColor(Color(red: 0.7, green: 0.7, blue: 0.7))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(red: 0.08, green: 0.08, blue: 0.08))
        .id(localizationManager.currentLanguage)
    }
}

// MARK: - Media Detail View (简单的全屏预览)
struct MediaDetailView: View {
    let item: MediaItem
    @Environment(\.presentationMode) var presentationMode
    @State private var image: UIImage?
    @ObservedObject private var photoLibraryManager = PhotoLibraryManager.shared
    
    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            if let image = image {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .ignoresSafeArea()
            } else {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.5)
            }
            
            // 关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white)
                            .frame(width: 44, height: 44)
                            .background(
                                Circle()
                                    .fill(Color.black.opacity(0.5))
                            )
                    }
                    .padding(.top, 50)
                    .padding(.trailing, 20)
                }
                Spacer()
            }
        }
        .onAppear {
            loadFullImage()
        }
    }
    
    private func loadFullImage() {
        photoLibraryManager.loadFullImage(for: item) { loadedImage in
            self.image = loadedImage
        }
    }
}

#Preview {
    MediaGridView(
        mediaType: .photos,
        sortOption: .dateDescending
    )
}