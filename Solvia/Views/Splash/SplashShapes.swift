//
//  SplashShapes.swift
//  Solvia
//
//  Created by <PERSON><PERSON> chen on 2025/7/24.
//

import SwiftUI

// 自定义星形形状
struct StarShape: Shape {
    let points: Int
    
    func path(in rect: CGRect) -> Path {
        let center = CGPoint(x: rect.width / 2, y: rect.height / 2)
        let radius = min(rect.width, rect.height) / 2
        let innerRadius = radius * 0.4
        
        var path = Path()
        
        for i in 0..<points * 2 {
            let angle = Double(i) * .pi / Double(points)
            let currentRadius = i % 2 == 0 ? radius : innerRadius
            let x = center.x + CGFloat(cos(angle)) * currentRadius
            let y = center.y + CGFloat(sin(angle)) * currentRadius
            
            if i == 0 {
                path.move(to: CGPoint(x: x, y: y))
            } else {
                path.addLine(to: CGPoint(x: x, y: y))
            }
        }
        
        path.closeSubpath()
        return path
    }
}

// 自定义三角形形状
struct Triangle: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        path.move(to: CGPoint(x: rect.midX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.midX, y: rect.minY))
        
        return path
    }
}