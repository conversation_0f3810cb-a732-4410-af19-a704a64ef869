//
//  SplashAnimationView.swift
//  Solvia
//
//  Created by <PERSON><PERSON> chen on 2025/7/24.
//

import SwiftUI

struct SplashAnimationView: View {
    @State private var logoScale: CGFloat = 0.6
    @State private var logoOpacity: Double = 0.0
    @State private var backgroundElementsOpacity: Double = 0.0
    @State private var rotationAngle: Double = 0
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 完全按照设计图的深色背景
                Color(red: 0.08, green: 0.08, blue: 0.08)
                    .ignoresSafeArea()
                

                
                // 背景装饰元素 - 完全按照设计图位置和形状
                ZStack {
                    // 左上角叶子形状
                    Ellipse()
                        .fill(Color.gray.opacity(0.06))
                        .frame(width: 140, height: 90)
                        .rotationEffect(.degrees(35))
                        .position(x: geometry.size.width * 0.12, y: geometry.size.height * 0.22)
                        .opacity(backgroundElementsOpacity)
                    
                    // 右下角叶子形状
                    Ellipse()
                        .fill(Color.gray.opacity(0.05))
                        .frame(width: 160, height: 100)
                        .rotationEffect(.degrees(-25))
                        .position(x: geometry.size.width * 0.88, y: geometry.size.height * 0.78)
                        .opacity(backgroundElementsOpacity)
                    
                    // 左下角星形 - 6角星
                    StarShape(points: 6)
                        .fill(Color.gray.opacity(0.08))
                        .frame(width: 60, height: 60)
                        .position(x: geometry.size.width * 0.15, y: geometry.size.height * 0.85)
                        .rotationEffect(.degrees(rotationAngle))
                        .opacity(backgroundElementsOpacity)
                    
                    // 右上角三角形
                    Triangle()
                        .fill(Color.gray.opacity(0.06))
                        .frame(width: 40, height: 40)
                        .position(x: geometry.size.width * 0.85, y: geometry.size.height * 0.18)
                        .rotationEffect(.degrees(-rotationAngle * 0.5))
                        .opacity(backgroundElementsOpacity)
                    
                    // 中间偏右的圆形
                    Circle()
                        .fill(Color.gray.opacity(0.04))
                        .frame(width: 100, height: 100)
                        .position(x: geometry.size.width * 0.75, y: geometry.size.height * 0.45)
                        .opacity(backgroundElementsOpacity)
                }
                
                // 主要内容区域
                VStack(spacing: 0) {
                    Spacer()
                    
                    // 主要Logo区域 - 完全按照设计图
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(red: 0.45, green: 0.65, blue: 0.25)) // 更准确的绿色
                        .frame(width: 260, height: 110)
                        .overlay(
                            VStack(spacing: -4) {
                                Text("MATATA")
                                    .font(.system(size: 36, weight: .black, design: .default))
                                    .foregroundColor(.white)
                                    .tracking(3)
                                
                                Text("XPLORE")
                                    .font(.system(size: 36, weight: .black, design: .default))
                                    .foregroundColor(.white)
                                    .tracking(3)
                            }
                        )
                        .scaleEffect(logoScale)
                        .opacity(logoOpacity)
                        .shadow(color: Color.black.opacity(0.4), radius: 15, x: 0, y: 8)
                    
                    Spacer()
                    
                    // 底部 "Power by MatataStudio" 文字 - 单行显示
                    Text("Power by Matatastudio")
                        .font(.system(size: 13, weight: .light))
                        .foregroundColor(.white.opacity(0.7))
                        .tracking(0.5)
                        .opacity(logoOpacity)
                    .padding(.bottom, 50)
                }
            }
        }
        .preferredColorScheme(.dark) // 确保状态栏内容为白色
        .onAppear {
            startLaunchAnimation()
        }
    }
    
    private func startLaunchAnimation() {
        // 背景元素淡入
        withAnimation(.easeIn(duration: 0.6)) {
            backgroundElementsOpacity = 1.0
        }
        
        // Logo出现动画 - 更戏剧性的效果
        withAnimation(.spring(response: 1.2, dampingFraction: 0.6, blendDuration: 0).delay(0.2)) {
            logoScale = 1.0
            logoOpacity = 1.0
        }
        
        // 背景元素缓慢旋转
        withAnimation(.linear(duration: 30).repeatForever(autoreverses: false)) {
            rotationAngle = 360
        }
    }
}