//
//  AddDeviceView.swift
//  Solvia
//
//  Created by <PERSON><PERSON> chen on 2025/7/24.
//

import SwiftUI

// MARK: - Navigation Bar Style Modifier
struct NavigationBarStyleModifier: ViewModifier {
    func body(content: Content) -> some View {
        if #available(iOS 16.0, *) {
            content
                .toolbarBackground(.hidden, for: .navigationBar)
                .toolbarColorScheme(.dark, for: .navigationBar)
                .navigationBarTitleDisplayMode(.inline)
        } else {
            content
                .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct AddDeviceView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var selectedDevice: DeviceType? = nil
    @State private var showingConnectionGuide = false
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    private func dismissView() {
        if #available(iOS 15.0, *) {
            // 使用新的dismiss方式
            presentationMode.wrappedValue.dismiss()
        } else {
            // 兼容旧版本
            presentationMode.wrappedValue.dismiss()
        }
    }

    private func setupNavigationBarWithoutDivider() {
        // 避免在预览环境中设置全局外观
        guard ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] != "1" else {
            return
        }
        
        let appearance = UINavigationBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor(red: 0.08, green: 0.08, blue: 0.08, alpha: 1.0)
        appearance.titleTextAttributes = [
            .foregroundColor: UIColor.white,
            .font: UIFont.systemFont(ofSize: 17, weight: .semibold)
        ]
        
        // 完全移除分割线
        appearance.shadowColor = UIColor.clear
        appearance.shadowImage = UIImage()
        appearance.backgroundEffect = nil
        
        // 设置所有状态的外观
        UINavigationBar.appearance().standardAppearance = appearance
        UINavigationBar.appearance().compactAppearance = appearance
        UINavigationBar.appearance().scrollEdgeAppearance = appearance
        
        if #available(iOS 15.0, *) {
            UINavigationBar.appearance().compactScrollEdgeAppearance = appearance
        }
        
        UINavigationBar.appearance().tintColor = UIColor.white
    }
    
    enum DeviceType: CaseIterable {
        case binoculars
        case monocular
        
        var titleKey: String {
            switch self {
            case .binoculars:
                return "add_device.binoculars"
            case .monocular:
                return "add_device.monocular"
            }
        }
        
        var iconName: String {
            switch self {
            case .binoculars:
                return "ai_binoculars"
            case .monocular:
                return "ai_monocular"
            }
        }
    }
    
    var body: some View {
        ZStack {
            Color(red: 0.08, green: 0.08, blue: 0.08)
                .ignoresSafeArea()
            


            VStack(spacing: 0) {
                // 自定义导航栏
                HStack {
                    Button(action: {
                        dismissView()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white)
                    }
                    
                    Spacer()
                    
                    LocalizedText("add_device.select_device")
                        .font(.system(size: 17, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    // 占位符，保持标题居中
                    Image(systemName: "chevron.left")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.clear)
                }
                .padding(.horizontal, 20)
                .padding(.top, 10) // 减少顶部距离，与连接指导页面对齐
                .padding(.bottom, 10)
                
                // 设备选择区域
                HStack(spacing: 20) {
                    // 双筒望远镜
                    DeviceSelectionCard(
                        deviceType: .binoculars,
                        isSelected: selectedDevice == .binoculars,
                        action: { selectedDevice = .binoculars }
                    )
                    
                    // 单筒望远镜
                    DeviceSelectionCard(
                        deviceType: .monocular,
                        isSelected: selectedDevice == .monocular,
                        action: { selectedDevice = .monocular }
                    )
                }
                .padding(.horizontal, 30)
                .padding(.top, 40) // 调整顶部距离
                
                Spacer()
                
                // Next按钮
                Button(action: {
                    showingConnectionGuide = true
                }) {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(red: 0.45, green: 0.65, blue: 0.25))
                        .frame(height: 50)
                        .overlay(
                            LocalizedText("add_device.next")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                        )
                }
                .padding(.horizontal, 30)
                .padding(.bottom, 30)
                .disabled(selectedDevice == nil)
                .opacity(selectedDevice == nil ? 0.5 : 1.0)
            }
        }
        .navigationBarHidden(true)
        .id(localizationManager.currentLanguage)
        .onAppear {
            // 直接设置导航栏外观来移除分割线
            setupNavigationBarWithoutDivider()
        }
        .modifier(NavigationBarStyleModifier())
        .preferredColorScheme(.dark)
        .background(
            // 使用NavigationLink进行导航，兼容iOS 15
            NavigationLink(
                destination: DeviceConnectionGuideView(),
                isActive: $showingConnectionGuide
            ) {
                EmptyView()
            }
            .hidden()
        )
    }
}