//
//  PermissionRequestView.swift
//  Solvia
//
//  Created by <PERSON><PERSON> chen on 2025/7/25.
//

import SwiftUI

struct PermissionRequestView: View {
    let titleKey: String
    let messageKey: String
    let onAllow: () -> Void
    let onDeny: () -> Void
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.5)
                .ignoresSafeArea()
            
            // 弹框内容
            VStack(spacing: 20) {
                // 图标
                Image(systemName: "location.circle.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.blue)
                
                // 标题
                LocalizedText(titleKey)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                
                // 消息
                LocalizedText(messageKey)
                    .font(.system(size: 14))
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)
                    .padding(.horizontal, 20)
                
                // 按钮
                HStack(spacing: 12) {
                    // 拒绝按钮
                    Button(action: onDeny) {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(red: 0.3, green: 0.3, blue: 0.3))
                            .frame(height: 44)
                            .overlay(
                                LocalizedText("permission.deny")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                            )
                    }
                    
                    // 允许按钮
                    Button(action: onAllow) {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(red: 0.45, green: 0.65, blue: 0.25))
                            .frame(height: 44)
                            .overlay(
                                LocalizedText("permission.allow")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                            )
                    }
                }
                .padding(.horizontal, 20)
            }
            .padding(.all, 24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(red: 0.15, green: 0.15, blue: 0.15))
            )
            .padding(.horizontal, 40)
        }
        .id(localizationManager.currentLanguage)
    }
}

// MARK: - WiFi Permission Request View
struct WiFiPermissionRequestView: View {
    let onAllow: () -> Void
    let onDeny: () -> Void
    
    var body: some View {
        PermissionRequestView(
            titleKey: "permission.wifi.title",
            messageKey: "permission.wifi.message",
            onAllow: onAllow,
            onDeny: onDeny
        )
    }
}

#Preview {
    WiFiPermissionRequestView(
        onAllow: {},
        onDeny: {}
    )
}