//
//  WiFiTestView.swift
//  Solvia
//
//  Created by <PERSON><PERSON> chen on 2025/7/25.
//

import SwiftUI

struct WiFiTestView: View {
    @StateObject private var wifiManager = WiFiManager.shared
    @State private var selectedSSID = "Solvia_Binocular_001"
    
    let testSSIDs = [
        "Solvia_Binocular_001",
        "Solvia_Monocular_002",
        "Solvia_Unknown_003",
        "MyHomeWiFi",
        "OfficeNetwork"
    ]
    
    var body: some View {
        VStack(spacing: 20) {
            Text("WiFi Connection Test")
                .font(.title2)
                .foregroundColor(.white)
            
            Text("Select a WiFi network to simulate:")
                .foregroundColor(.gray)
            
            Picker("WiFi Network", selection: $selectedSSID) {
                ForEach(testSSIDs, id: \.self) { ssid in
                    Text(ssid).tag(ssid)
                }
            }
            .pickerStyle(WheelPickerStyle())
            .background(Color(red: 0.15, green: 0.15, blue: 0.15))
            .cornerRadius(8)
            
            Button("Simulate Connection") {
                simulateWiFiConnection()
            }
            .padding()
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(8)
            
            if let currentSSID = wifiManager.currentSSID {
                VStack(spacing: 8) {
                    Text("Current WiFi: \(currentSSID)")
                        .foregroundColor(.white)
                    
                    Text("Is Solvia Device: \(wifiManager.isConnectedToSolviaDevice() ? "Yes" : "No")")
                        .foregroundColor(wifiManager.isConnectedToSolviaDevice() ? .green : .red)
                    
                    if let deviceType = wifiManager.getDeviceTypeFromSSID() {
                        Text("Device Type: \(deviceType)")
                            .foregroundColor(.white)
                    }
                }
                .padding()
                .background(Color(red: 0.15, green: 0.15, blue: 0.15))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(red: 0.08, green: 0.08, blue: 0.08))
    }
    
    private func simulateWiFiConnection() {
        wifiManager.currentSSID = selectedSSID
    }
}

#Preview {
    WiFiTestView()
}