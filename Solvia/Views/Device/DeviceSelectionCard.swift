//
//  DeviceSelectionCard.swift
//  Solvia
//
//  Created by <PERSON><PERSON> chen on 2025/7/24.
//

import SwiftUI

struct DeviceSelectionCard: View {
    let deviceType: AddDeviceView.DeviceType
    let isSelected: Bool
    let action: () -> Void
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    var body: some View {
        Button(action: action) {
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(red: 0.15, green: 0.15, blue: 0.15))
                .frame(width: 160, height: 200)
                .overlay(
                    VStack(spacing: 20) {
                        // 设备图标
                        DeviceIcon(deviceType: deviceType)
                        
                        // 设备名称
                        LocalizedText(deviceType.titleKey)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                            .lineLimit(2)
                    }
                    .padding(.vertical, 20)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            isSelected ? Color(red: 0.45, green: 0.65, blue: 0.25) : Color.clear,
                            lineWidth: 2
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
        .id(localizationManager.currentLanguage)
    }
}