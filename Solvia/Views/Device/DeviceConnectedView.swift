//
//  DeviceConnectedView.swift
//  Solvia
//
//  Created by <PERSON><PERSON> chen on 2025/7/25.
//

import SwiftUI

struct DeviceConnectedView: View {
    @Environment(\.presentationMode) var presentationMode
    let deviceType: String
    let ssid: String
    @ObservedObject private var localizationManager = LocalizationManager.shared
    @ObservedObject private var deviceManager = DeviceManager.shared
    
    private func dismissView() {
        presentationMode.wrappedValue.dismiss()
    }
    
    private func saveDevice() {
        // 将设备类型字符串转换为DeviceType枚举
        let type: SolviaDevice.DeviceType = deviceType == "binoculars" ? .binoculars : .monocular
        
        // 创建新设备
        let newDevice = SolviaDevice(
            name: "Solvia 8x32",
            type: type,
            ssid: ssid,
            isConnected: true,
            dateAdded: Date()
        )
        
        // 添加到设备管理器
        deviceManager.addDevice(newDevice)
    }
    
    var body: some View {
        ZStack {
            Color(red: 0.08, green: 0.08, blue: 0.08)
                .ignoresSafeArea()
            
            VStack(spacing: 40) {
                // 成功图标
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.green)
                
                // 成功信息
                VStack(spacing: 16) {
                    LocalizedText("device.connected.title")
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(.white)
                    
                    LocalizedText("device.connected.subtitle")
                        .font(.system(size: 16))
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                }
                
                // 设备信息
                VStack(spacing: 12) {
                    HStack {
                        LocalizedText("device.connected.type")
                            .foregroundColor(.gray)
                        Spacer()
                        Text(getDeviceTypeName(deviceType))
                            .foregroundColor(.white)
                    }
                    
                    HStack {
                        LocalizedText("device.connected.network")
                            .foregroundColor(.gray)
                        Spacer()
                        Text(ssid)
                            .foregroundColor(.white)
                    }
                }
                .padding(.all, 20)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(red: 0.15, green: 0.15, blue: 0.15))
                )
                .padding(.horizontal, 40)
                
                Spacer()
                
                // 继续按钮
                Button(action: {
                    // 保存设备到设备管理器
                    saveDevice()
                    // 返回主页
                    dismissView()
                }) {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(red: 0.45, green: 0.65, blue: 0.25))
                        .frame(height: 50)
                        .overlay(
                            LocalizedText("device.connected.continue")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.white)
                        )
                }
                .padding(.horizontal, 40)
                .padding(.bottom, 40)
            }
        }
        .navigationBarHidden(true)
        .preferredColorScheme(.dark)
        .id(localizationManager.currentLanguage)
    }
    
    private func getDeviceTypeName(_ type: String) -> String {
        switch type {
        case "binoculars":
            return "add_device.binoculars".localized
        case "monocular":
            return "add_device.monocular".localized
        default:
            return "device.connected.unknown".localized
        }
    }
}

#Preview {
    DeviceConnectedView(deviceType: "binoculars", ssid: "Solvia_Binocular_001")
}