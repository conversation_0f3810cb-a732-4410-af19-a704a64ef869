//
//  DeviceCardView.swift
//  Solvia
//
//  Created by Ki<PERSON> on 2025/7/25.
//

import SwiftUI

struct DeviceCardView: View {
    let device: SolviaDevice
    let onConnectTap: () -> Void
    let onEnterTap: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            // 设备卡片 - 只包含设备信息，不包含按钮
            RoundedRectangle(cornerRadius: 32)
                .fill(Color(red: 0.15, green: 0.15, blue: 0.15))
                .frame(width: 280, height: 420) // 与添加设备卡片保持相同高度
                .overlay(
                    ZStack {
                        // 设备图标 - 完全居中显示
                        Image(device.type.iconName)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 140, height: 140) // 增大图片尺寸从100到140
                            .foregroundColor(device.isConnected ? .white : .gray)
                            .opacity(device.isConnected ? 1.0 : 0.6)

                        // 设备名称和类型 - 绝对定位到顶部
                        VStack {
                            VStack(spacing: 8) {
                                Text(device.name)
                                    .font(.system(size: 20, weight: .semibold))
                                    .foregroundColor(device.isConnected ? .white : .gray)

                                Text(device.type.displayName)
                                    .font(.system(size: 14))
                                    .foregroundColor(device.isConnected ? .white.opacity(0.8) : .gray)
                                    .multilineTextAlignment(.center)
                            }
                            .padding(.top, 40)

                            Spacer()
                        }
                    }
                )

            // 操作按钮 - 移到卡片外部底部
            Button(action: device.isConnected ? onEnterTap : onConnectTap) {
                Text(device.isConnected ? "device.button.enter".localized : "device.button.connect".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(width: 150, height: 36) // 减小按钮高度，更符合设计图比例
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(device.isConnected ?
                                  Color(red: 0.45, green: 0.65, blue: 0.25) : // 绿色
                                  Color(red: 0.3, green: 0.3, blue: 0.3))     // 灰色
                    )
            }
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        // 已连接状态预览
        DeviceCardView(
            device: SolviaDevice(
                name: "Solvia 8x32",
                type: .binoculars,
                ssid: "Solvia_Binocular_001",
                isConnected: true,
                dateAdded: Date(),
                hasConnectedBefore: true
            ),
            onConnectTap: {},
            onEnterTap: {}
        )

        // 未连接状态预览
        DeviceCardView(
            device: SolviaDevice(
                name: "Solvia 8x32",
                type: .binoculars,
                ssid: "Solvia_Binocular_001",
                isConnected: false,
                dateAdded: Date(),
                hasConnectedBefore: false
            ),
            onConnectTap: {},
            onEnterTap: {}
        )
    }
    .padding()
    .background(Color(red: 0.08, green: 0.08, blue: 0.08))
}
