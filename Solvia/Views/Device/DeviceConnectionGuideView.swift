//
//  DeviceConnectionGuideView.swift
//  Solvia
//
//  Created by si<PERSON> chen on 2025/7/25.
//

import SwiftUI
import UIKit

struct DeviceConnectionGuideView: View {
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var wifiManager = WiFiManager.shared
    @State private var showingPermissionRequest = false
    @State private var showingConnectionStatus = false
    @State private var connectionMessage = ""
    @State private var isCheckingConnection = false
    @State private var showingDeviceConnected = false
    @State private var connectedDeviceType = ""
    @State private var connectedSSID = ""
    @State private var showingPermissionDeniedAlert = false
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    private func dismissView() {
        presentationMode.wrappedValue.dismiss()
    }
    
    private func handleGoButtonTap() {
        // 检查位置权限状态
        let permissionStatus = wifiManager.getLocationPermissionStatus()
        
        switch permissionStatus {
        case .authorizedWhenInUse, .authorizedAlways:
            // 已有权限，直接跳转到系统WiFi设置页面
            openWiFiSettings()
            
        case .denied, .restricted:
            // 权限被拒绝，显示引导用户到应用设置的弹框
            showPermissionDeniedAlert()
            
        case .notDetermined:
            // 权限未确定，显示说明弹框
            showingPermissionRequest = true
            
        @unknown default:
            // 未知状态，显示说明弹框
            showingPermissionRequest = true
        }
    }
    
    private func requestPermissionAndOpenSettings() {
        showingPermissionRequest = false

        // 请求位置权限（这会触发系统权限弹框）
        print("正在请求位置权限...")
        wifiManager.requestLocationPermission()

        // 权限状态变化会通过 onChange(of: wifiManager.isLocationPermissionGranted) 监听
        // 如果用户授予权限，会自动跳转到WiFi设置页面
    }
    
    private func openWiFiSettings() {
        wifiManager.openWiFiSettings()
    }
    
    private func openAppSettings() {
        wifiManager.openAppSettings()
    }
    
    private func showPermissionDeniedAlert() {
        showingPermissionDeniedAlert = true
    }
    
    private func checkWiFiConnection() {
        isCheckingConnection = true
        connectionMessage = "device.connection.checking".localized
        showingConnectionStatus = true
        
        // 延迟检查，给WiFi连接一些时间
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            if wifiManager.isConnectedToSolviaDevice() {
                let deviceType = wifiManager.getDeviceTypeFromSSID()
                let ssid = wifiManager.getCurrentWiFiSSID() ?? "Unknown"
                
                if let type = deviceType, type != "unknown_solvia_device" {
                    connectionMessage = "device.connection.success".localized
                    connectedDeviceType = type
                    connectedSSID = ssid
                    
                    // 连接成功，跳转到设备连接成功页面
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                        showingConnectionStatus = false
                        showingDeviceConnected = true
                    }
                } else {
                    connectionMessage = "device.connection.unknown_device".localized
                    connectedDeviceType = "unknown"
                    connectedSSID = ssid
                    
                    // 未知设备类型，也跳转到连接成功页面
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                        showingConnectionStatus = false
                        showingDeviceConnected = true
                    }
                }
            } else {
                connectionMessage = "device.connection.failed".localized
            }
            
            isCheckingConnection = false
            
            // 如果连接失败，3秒后隐藏状态提示
            if !wifiManager.isConnectedToSolviaDevice() {
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    showingConnectionStatus = false
                }
            }
        }
    }
    
    private func setupNavigationBarWithoutDivider() {
        // 避免在预览环境中设置全局外观
        guard ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] != "1" else {
            return
        }
        
        let appearance = UINavigationBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor(red: 0.08, green: 0.08, blue: 0.08, alpha: 1.0)
        appearance.titleTextAttributes = [
            .foregroundColor: UIColor.white,
            .font: UIFont.systemFont(ofSize: 17, weight: .semibold)
        ]
        
        // 完全移除分割线
        appearance.shadowColor = UIColor.clear
        appearance.shadowImage = UIImage()
        appearance.backgroundEffect = nil
        
        // 设置所有状态的外观
        UINavigationBar.appearance().standardAppearance = appearance
        UINavigationBar.appearance().compactAppearance = appearance
        UINavigationBar.appearance().scrollEdgeAppearance = appearance
        
        if #available(iOS 15.0, *) {
            UINavigationBar.appearance().compactScrollEdgeAppearance = appearance
        }
        
        UINavigationBar.appearance().tintColor = UIColor.white
    }
    
    var body: some View {
        ZStack {
            Color(red: 0.08, green: 0.08, blue: 0.08)
                .ignoresSafeArea()
            
            // 在导航栏下方添加遮盖层来隐藏分割线
            VStack {
                Rectangle()
                    .fill(Color(red: 0.08, green: 0.08, blue: 0.08))
                    .frame(height: 2)
                    .offset(y: -1)
                Spacer()
            }
            .ignoresSafeArea(.container, edges: .top)
            
            VStack(spacing: 0) {
                // 使用ZStack来实现WiFi图标叠加效果
                ZStack(alignment: .topLeading) {
                    // 主要内容区域 - 一个大的圆角矩形容器
                    RoundedRectangle(cornerRadius: 32)
                        .fill(Color(red: 0.15, green: 0.15, blue: 0.15))
                        .overlay(
                            VStack(spacing: 0) {
                                // 固定的顶部WiFi提示区域 - 不参与滚动
                                VStack(spacing: 12) {
                                    // 为叠加的WiFi图标留出空间
                                    Spacer()
                                        .frame(height: 35)
                                    
                                    // 提示文本
                                    LocalizedText("connection_guide.wifi_reminder")
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.white)
                                        .multilineTextAlignment(.leading)
                                        .lineLimit(nil)
                                        .padding(.horizontal, 20)
                                }
                                .padding(.top, 0)
                                .padding(.bottom, 20)
                                
                                // 可滚动的步骤区域
                                ScrollView {
                                    VStack(spacing: 24) {
                                        // 步骤1
                                        ConnectionStepView(
                                            stepNumber: 1,
                                            titleKey: "connection_guide.step1_title",
                                            descriptionKey: "connection_guide.step1_description",
                                            imageName: "ic_guide_step1"
                                        )
                                        
                                        // 步骤2
                                        ConnectionStepView(
                                            stepNumber: 2,
                                            titleKey: "connection_guide.step2_title",
                                            descriptionKey: "connection_guide.step2_description",
                                            imageName: "ic_guide_step2"
                                        )
                                        
                                        // 步骤3
                                        ConnectionStepView(
                                            stepNumber: 3,
                                            titleKey: "connection_guide.step3_title",
                                            descriptionKey: "connection_guide.step3_description",
                                            imageName: "ic_guide_step3"
                                        )
                                    }
                                    .padding(.horizontal, 24)
                                    .padding(.top, 15) // 增加顶部边距，避免序号被覆盖
                                    .padding(.bottom, 30)
                                }
                            }

                        )
                        .padding(.horizontal, 36)
                        .padding(.top, 40) // 为WiFi图标留出更多空间
                        .frame(minHeight: 660) // 增加卡片最小高度以显示更多内容
                    
                    // WiFi图标 - 叠加在卡片上方，位置在左上角1/3处，一半在外一半在内
                    Image("ic_guide_wifi")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 60, height: 60)
                        .offset(x: 90, y: 10) // 调整位置：x轴约1/3位置，y轴让一半在卡片外
                }
                .padding(.top, 20)
                
                Spacer(minLength: 20)
                
                // GO按钮
                Button(action: {
                    handleGoButtonTap()
                }) {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(red: 0.45, green: 0.65, blue: 0.25))
                        .frame(width: 200, height: 50) // 设置固定宽度，使按钮更紧凑
                        .overlay(
                            LocalizedText("connection_guide.go_button")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.white)
                        )
                }
                .padding(.bottom, 40)
            }
        }
        .navigationTitle("connection_guide.title".localized)
        .id(localizationManager.currentLanguage)
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    dismissView()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                }
            }
        }
        .onAppear {
            // 直接设置导航栏外观来移除分割线
            setupNavigationBarWithoutDivider()
        }
        .background(
            // 在导航栏下方添加一个与背景色相同的矩形来遮盖分割线
            Rectangle()
                .fill(Color(red: 0.08, green: 0.08, blue: 0.08))
                .frame(height: 1)
                .offset(y: -1)
                .ignoresSafeArea(.container, edges: .top)
        )
        .preferredColorScheme(.dark)
        .overlay(
            // 权限请求弹框
            Group {
                if showingPermissionRequest {
                    WiFiPermissionRequestView(
                        onAllow: {
                            requestPermissionAndOpenSettings()
                        },
                        onDeny: {
                            showingPermissionRequest = false
                        }
                    )
                    .transition(.opacity)
                }
            }
        )
        .overlay(
            // 权限被拒绝提示弹框
            Group {
                if showingPermissionDeniedAlert {
                    PermissionDeniedAlertView(
                        onOpenSettings: {
                            showingPermissionDeniedAlert = false
                            openAppSettings()
                        },
                        onCancel: {
                            showingPermissionDeniedAlert = false
                        }
                    )
                    .transition(.opacity)
                }
            }
        )
        .overlay(
            // 连接状态提示
            Group {
                if showingConnectionStatus {
                    ConnectionStatusView(
                        message: connectionMessage,
                        isLoading: isCheckingConnection
                    )
                    .transition(.opacity)
                }
            }
        )
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
            // 当应用从后台返回前台时检查WiFi连接
            if wifiManager.isLocationPermissionGranted {
                checkWiFiConnection()
            }
        }
        .onChange(of: wifiManager.isLocationPermissionGranted) { isGranted in
            // 监听权限状态变化
            if isGranted {
                // 权限被授予，立即跳转到WiFi设置
                print("权限已授予，跳转到WiFi设置")
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    openWiFiSettings()
                }
            }
        }
        .background(
            // 使用NavigationLink进行导航到设备连接成功页面
            NavigationLink(
                destination: DeviceConnectedView(
                    deviceType: connectedDeviceType,
                    ssid: connectedSSID
                ),
                isActive: $showingDeviceConnected
            ) {
                EmptyView()
            }
            .hidden()
        )
    }
}

// MARK: - Connection Step View
struct ConnectionStepView: View {
    let stepNumber: Int
    let titleKey: String
    let descriptionKey: String
    let imageName: String
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    var body: some View {
        // 使用ZStack实现序号叠加效果
        ZStack(alignment: .topLeading) {
            // 步骤内容容器 - 添加细边框，更大的圆角
            VStack(alignment: .leading, spacing: 12) {
                // 内容区域 - 为叠加的序号留出空间
                VStack(alignment: .leading, spacing: 8) {
                    // 标题
                    LocalizedText(titleKey)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.leading)
                        .padding(.leading, 18) // 调整左侧空间
                    
                    // 描述
                    LocalizedText(descriptionKey)
                        .font(.system(size: 13))
                        .foregroundColor(Color(red: 0.7, green: 0.7, blue: 0.7))
                        .multilineTextAlignment(.leading)
                        .lineLimit(nil)
                        .padding(.leading, 18) // 调整左侧空间
                }
                .padding(.top, 8) // 调整顶部空间
                
                // 示意图 - 全宽显示
                if !imageName.isEmpty {
                    HStack {
                        Spacer()
                        Image(imageName)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(maxHeight: 140)
                            .cornerRadius(8)
                        Spacer()
                    }
                    .padding(.top, 8)
                }
            }
            .padding(.all, 20) // 增加内边距
            .overlay(
                // 为整个步骤添加细边框，更大的圆角，更细的线条，更暗的颜色
                RoundedRectangle(cornerRadius: 20)
                    .stroke(Color(red: 0.3, green: 0.3, blue: 0.3), lineWidth: 0.5)
            )
            
            // 序号 - 圆形背景，叠加在左上角，一半在外面
            Circle()
                .fill(Color(red: 0.15, green: 0.15, blue: 0.15))
                .frame(width: 36, height: 36)
                .overlay(
                    Text("\(stepNumber)")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.white)
                )
                .overlay(
                    Circle()
                        .stroke(Color(red: 0.25, green: 0.25, blue: 0.25), lineWidth: 0.5)
                )
                .offset(x: -12, y: -12) // 调整位置，往右下角移动一些
        }
        .id(localizationManager.currentLanguage)
    }
}

// MARK: - Permission Denied Alert View
struct PermissionDeniedAlertView: View {
    let onOpenSettings: () -> Void
    let onCancel: () -> Void
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.5)
                .ignoresSafeArea()
            
            // 弹框内容
            VStack(spacing: 20) {
                // 图标
                Image(systemName: "location.slash.circle.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.orange)
                
                // 标题
                LocalizedText("permission.denied.title")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                
                // 消息
                LocalizedText("permission.denied.message")
                    .font(.system(size: 14))
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)
                    .padding(.horizontal, 20)
                
                // 按钮
                HStack(spacing: 12) {
                    // 取消按钮
                    Button(action: onCancel) {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(red: 0.3, green: 0.3, blue: 0.3))
                            .frame(height: 44)
                            .overlay(
                                LocalizedText("common.cancel")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                            )
                    }
                    
                    // 去设置按钮
                    Button(action: onOpenSettings) {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(red: 0.45, green: 0.65, blue: 0.25))
                            .frame(height: 44)
                            .overlay(
                                LocalizedText("permission.denied.go_settings")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                            )
                    }
                }
                .padding(.horizontal, 20)
            }
            .padding(.all, 24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(red: 0.15, green: 0.15, blue: 0.15))
            )
            .padding(.horizontal, 40)
        }
        .id(localizationManager.currentLanguage)
    }
}

// MARK: - Connection Status View
struct ConnectionStatusView: View {
    let message: String
    let isLoading: Bool
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.5)
                .ignoresSafeArea()
            
            // 状态提示框
            VStack(spacing: 16) {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.2)
                } else {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.green)
                }
                
                Text(message)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
            .padding(.all, 24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(red: 0.15, green: 0.15, blue: 0.15))
            )
            .padding(.horizontal, 60)
        }
    }
}

#Preview {
    if #available(iOS 16.0, *) {
        NavigationStack {
            DeviceConnectionGuideView()
        }
    } else {
        NavigationView {
            DeviceConnectionGuideView()
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }
}
