//
//  DeviceStateTestView.swift
//  Solvia
//
//  Created by Augment Agent on 2025/7/25.
//

import SwiftUI

/// 用于测试设备连接状态的视图
struct DeviceStateTestView: View {
    @State private var isConnected = false
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    var body: some View {
        ZStack {
            // 深色背景
            Color(red: 0.08, green: 0.08, blue: 0.08)
                .ignoresSafeArea()
            
            VStack(spacing: 40) {
                // 标题
                Text("设备状态测试")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.top, 80)
                
                // 设备卡片
                DeviceCardView(
                    device: SolviaDevice(
                        name: "Solvia 8x32",
                        type: .binoculars,
                        ssid: "Solvia_Binocular_001",
                        isConnected: isConnected,
                        dateAdded: Date(),
                        hasConnectedBefore: isConnected
                    ),
                    onConnectTap: {
                        print("点击连接按钮")
                        // 模拟连接过程
                        withAnimation(.easeInOut(duration: 0.5)) {
                            isConnected = true
                        }
                    },
                    onEnterTap: {
                        print("点击进入按钮")
                        // 这里可以导航到设备主界面
                    }
                )
                .padding(.horizontal, 40)
                
                // 状态切换按钮
                VStack(spacing: 16) {
                    Text("当前状态: \(isConnected ? "已连接" : "未连接")")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.5)) {
                            isConnected.toggle()
                        }
                    }) {
                        Text("切换连接状态")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.blue)
                            )
                    }
                }
                
                Spacer()
            }
        }
        .navigationBarHidden(true)
        .preferredColorScheme(.dark)
        .id(localizationManager.currentLanguage)
    }
}

#Preview {
    DeviceStateTestView()
}
