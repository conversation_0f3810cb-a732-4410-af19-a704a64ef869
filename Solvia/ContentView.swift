//
//  ContentView.swift
//  test
//
//  Created by <PERSON><PERSON> chen on 2025/7/24.
//

import SwiftUI

struct ContentView: View {
    @State private var showingSplash = true
    
    var body: some View {
        if showingSplash {
            // 应用内启动动画
            SplashAnimationView()
                .onAppear {
                    // 短暂延迟后显示主界面
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                        withAnimation(.easeInOut(duration: 0.5)) {
                            showingSplash = false
                        }
                    }
                }
        } else {
            // 主界面
            MainAppView()
        }
    }
}



struct MainAppView: View {
    @State private var selectedTab = 0
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // 观察页面 - 使用NavigationStack包装
            if #available(iOS 16.0, *) {
                NavigationStack {
                    HomeView()
                }
                .tabItem {
                    Image(selectedTab == 0 ? "ic_observation_selected" : "ic_observation")
                        .renderingMode(.template)
                    Text("tab.observation".localized)
                }
                .tag(0)
            } else {
                NavigationView {
                    HomeView()
                }
                .navigationViewStyle(StackNavigationViewStyle())
                .tabItem {
                    Image(selectedTab == 0 ? "ic_observation_selected" : "ic_observation")
                        .renderingMode(.template)
                    Text("tab.observation".localized)
                }
                .tag(0)
            }
            
            // 图库页面
            GalleryView()
                .tabItem {
                    Image(selectedTab == 1 ? "ic_gallery_selected" : "ic_gallery")
                        .renderingMode(.template)
                    Text("tab.gallery".localized)
                }
                .tag(1)
            
            // 日志页面
            LogView()
                .tabItem {
                    Image(selectedTab == 2 ? "ic_log_selected" : "ic_log")
                        .renderingMode(.template)
                    Text("tab.log".localized)
                }
                .tag(2)
            
            // 档案页面
            ProfileView()
                .tabItem {
                    Image(selectedTab == 3 ? "ic_profile_selected" : "ic_profile")
                        .renderingMode(.template)
                    Text("tab.profile".localized)
                }
                .tag(3)
        }
        .accentColor(.white)
        .preferredColorScheme(.dark)
        .id(localizationManager.currentLanguage) // 语言变化时重新渲染整个TabView
        .onAppear {
            setupTabBarAppearance()
        }
    }
    
    private func setupTabBarAppearance() {
        // 避免在预览环境中设置全局外观
        guard ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] != "1" else {
            return
        }
        
        // 使用 UITabBarAppearance 进行现代化配置
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor(red: 0.08, green: 0.08, blue: 0.08, alpha: 1.0)
        
        // 移除分割线
        appearance.shadowColor = UIColor.clear
        
        // 配置正常状态
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor.gray
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.gray,
            .font: UIFont.systemFont(ofSize: 10, weight: .medium)
        ]
        
        // 配置选中状态
        appearance.stackedLayoutAppearance.selected.iconColor = UIColor.white
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor.white,
            .font: UIFont.systemFont(ofSize: 10, weight: .medium)
        ]
        
        // 应用配置
        UITabBar.appearance().standardAppearance = appearance
        if #available(iOS 15.0, *) {
            UITabBar.appearance().scrollEdgeAppearance = appearance
        }
    }
}

// MARK: - Home View
struct HomeView: View {
    @ObservedObject private var deviceManager = DeviceManager.shared
    @ObservedObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        Group {
            if deviceManager.hasDevices {
                // 显示设备界面
                MyDeviceView()
            } else {
                // 显示添加设备界面
                AddDeviceView_Home()
            }
        }
        .navigationBarHidden(true)
    }
}

// MARK: - Add Device Home View
struct AddDeviceView_Home: View {
    @State private var showingAddDevice = false
    @ObservedObject private var localizationManager = LocalizationManager.shared

    var body: some View {
        ZStack {
            // 深色背景
            Color(red: 0.08, green: 0.08, blue: 0.08)
                .ignoresSafeArea()

            VStack(spacing: 0) {
                // 标题
                LocalizedText("home.add_device")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.top, 80)

                Spacer()

                // 添加设备按钮区域 - 完全按照设计图
                Button(action: {
                    showingAddDevice = true
                }) {
                    RoundedRectangle(cornerRadius: 32)
                        .fill(Color(red: 0.15, green: 0.15, blue: 0.15))
                        .frame(width: 280, height: 420)
                        .overlay(
                            // 加号图标 - 白色，居中
                            Image(systemName: "plus")
                                .font(.system(size: 55, weight: .medium))
                                .foregroundColor(.white)
                        )
                }
                .buttonStyle(PlainButtonStyle())

                Spacer()
                Spacer() // 额外的spacer来匹配设计图的比例
            }
        }
        .fullScreenCover(isPresented: $showingAddDevice) {
            if #available(iOS 16.0, *) {
                NavigationStack {
                    AddDeviceView()
                }
            } else {
                NavigationView {
                    AddDeviceView()
                }
                .navigationViewStyle(StackNavigationViewStyle())
            }
        }
    }
}

// MARK: - Gallery View (moved to separate file)
// GalleryView is now implemented in Views/Gallery/GalleryView.swift

// MARK: - Log View
struct LogView: View {
    var body: some View {
        ZStack {
            Color(red: 0.08, green: 0.08, blue: 0.08)
                .ignoresSafeArea()
            
            VStack {
                LocalizedText("tab.log")
                    .font(.title)
                    .foregroundColor(.white)
                
                Text("Log content coming soon...")
                    .foregroundColor(.gray)
                    .padding(.top)
            }
        }
    }
}

// MARK: - Profile View
struct ProfileView: View {
    var body: some View {
        ZStack {
            Color(red: 0.08, green: 0.08, blue: 0.08)
                .ignoresSafeArea()
            
            VStack {
                LocalizedText("tab.profile")
                    .font(.title)
                    .foregroundColor(.white)
                
                Text("Profile content coming soon...")
                    .foregroundColor(.gray)
                    .padding(.top)
            }
        }
    }
}

// MARK: - My Device View
struct MyDeviceView: View {
    @ObservedObject private var deviceManager = DeviceManager.shared
    @ObservedObject private var localizationManager = LocalizationManager.shared
    @ObservedObject private var wifiManager = WiFiManager.shared
    @State private var showingConnectionGuide = false
    @State private var showingAddDevice = false
    @State private var selectedDevice: SolviaDevice?
    
    var body: some View {
        ZStack {
            // 深色背景
            Color(red: 0.08, green: 0.08, blue: 0.08)
                .ignoresSafeArea()

            ZStack {
                VStack(spacing: 0) {
                    // 标题
                    LocalizedText("home.my_device")
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.top, 80)

                    Spacer()

                    // 设备卡片
                    if let device = deviceManager.devices.first {
                        DeviceCardView(
                            device: device,
                            onConnectTap: {
                                selectedDevice = device
                                // 如果设备曾经连接过，直接跳转到WiFi设置
                                if device.hasConnectedBefore {
                                    wifiManager.openWiFiSettings()
                                } else {
                                    showingConnectionGuide = true
                                }
                            },
                            onEnterTap: {
                                // TODO: 进入设备主界面
                                print("进入设备: \(device.name)")
                            }
                        )
                    }

                    Spacer()
                    Spacer()
                }

                // 右上角添加按钮
                VStack {
                    HStack {
                        Spacer()
                        Button(action: {
                            showingAddDevice = true
                        }) {
                            Image(systemName: "plus")
                                .font(.system(size: 24, weight: .medium))
                                .foregroundColor(.white)
                                .frame(width: 44, height: 44)
                        }
                        .padding(.top, 40) // 上移，从60减少到40
                        .padding(.trailing, 24)
                    }
                    Spacer()
                }
            }
        }
        .navigationBarHidden(true)
        .fullScreenCover(isPresented: $showingConnectionGuide) {
            if #available(iOS 16.0, *) {
                NavigationStack {
                    DeviceConnectionGuideView()
                }
            } else {
                NavigationView {
                    DeviceConnectionGuideView()
                }
                .navigationViewStyle(StackNavigationViewStyle())
            }
        }
        .fullScreenCover(isPresented: $showingAddDevice) {
            if #available(iOS 16.0, *) {
                NavigationStack {
                    AddDeviceView()
                }
            } else {
                NavigationView {
                    AddDeviceView()
                }
                .navigationViewStyle(StackNavigationViewStyle())
            }
        }
        .id(localizationManager.currentLanguage)
    }
}




#Preview {
    ContentView()
}
