/* 
  Localizable.strings
  Solvia

  Created by simon chen on 2025/7/24.
  
*/

// Tab Bar
"tab.observation" = "Observation";
"tab.gallery" = "Gallery";
"tab.log" = "Log";
"tab.profile" = "Profile";

// Home Screen
"home.add_device" = "Add your device";
"home.my_device" = "My Device";
"home.add_device_button" = "Add Device";

// Add Device Screen
"add_device.title" = "Add Device";
"add_device.select_device" = "Select your device";
"add_device.binoculars" = "Solvia 8x32\nAI Binoculars";
"add_device.monocular" = "Solvia 8x32\nAI Monocular";
"add_device.next" = "Next";

// Connection Guide
"connection_guide.title" = "Connect device";
"connection_guide.wifi_reminder" = "Before you begin, please ensure your phone's Wi-Fi is enabled.";
"connection_guide.step1_title" = "Activate Device WiFi";
"connection_guide.step1_description" = "Power on your device and activate its WiFi function.";
"connection_guide.step2_title" = "Connect to \"Solvia_xxxx\"";
"connection_guide.step2_description" = "On your phone, navigate to your WiFi settings, locate \"Solvia_xxxx\", and connect using the password: 12345678.";
"connection_guide.step3_title" = "Return to APP";
"connection_guide.step3_description" = "Once successfully connected, return to this APP to proceed.";
"connection_guide.go_button" = "GO";

// Permission Requests
"permission.wifi.title" = "WiFi Access Required";
"permission.wifi.message" = "To identify your connected Solvia device, we need to read your current WiFi network name. iOS requires location permission for WiFi access due to privacy protection. We only use this to detect your device, not to track your location.";
"permission.photo.title" = "Photo Library Access Required";
"permission.photo.message" = "To display your photos and videos in the gallery, we need access to your photo library. This allows you to view and manage your media content within the app.";
"permission.allow" = "Allow";
"permission.deny" = "Not Now";

// Photo Permission Status
"permission.photo.status.authorized" = "Photo Access Granted";
"permission.photo.status.limited" = "Limited Photo Access";
"permission.photo.status.denied" = "Photo Access Denied";
"permission.photo.status.restricted" = "Photo Access Restricted";
"permission.photo.status.not_determined" = "Photo Access Required";
"permission.photo.status.unknown" = "Unknown Photo Access Status";

// Photo Permission Descriptions
"permission.photo.description.authorized" = "You have granted full access to your photo library. All photos and videos are available.";
"permission.photo.description.limited" = "You have granted limited access to your photo library. Only selected photos are available.";
"permission.photo.description.denied" = "Photo library access has been denied. Please enable it in Settings to view your media.";
"permission.photo.description.restricted" = "Photo library access is restricted by device policies.";
"permission.photo.description.not_determined" = "Please grant access to your photo library to view your photos and videos.";
"permission.photo.description.unknown" = "Unable to determine photo library access status.";

// Photo Permission Actions
"permission.photo.request" = "Grant Access";
"permission.photo.open_settings" = "Open Settings";
"permission.photo.denied.title" = "Photo Access Required";
"permission.photo.denied.message" = "Photo library access has been denied. To view your photos and videos, please enable photo access in Settings.";
"permission.photo.denied.go_settings" = "Go to Settings";

// Permission Denied
"permission.denied.title" = "Location Permission Required";
"permission.denied.message" = "Location permission has been denied. To read WiFi information and connect to your Solvia device, please enable location permission in Settings.";
"permission.denied.go_settings" = "Go to Settings";

// Device Connection
"device.connection.checking" = "Checking WiFi connection...";
"device.connection.success" = "Device connected successfully!";
"device.connection.failed" = "Please connect to your Solvia device's WiFi first.";
"device.connection.unknown_device" = "Connected to Solvia device, but device type is unknown.";

// Device Connected
"device.connected.title" = "Device Connected!";
"device.connected.subtitle" = "Your Solvia device has been successfully connected and is ready to use.";
"device.connected.type" = "Device Type";
"device.connected.network" = "Network";
"device.connected.unknown" = "Unknown Device";
"device.connected.continue" = "Continue";

// Device Status
"device.status.connected" = "Connected";
"device.status.disconnected" = "Disconnected";
"device.button.connect" = "Connect";
"device.button.enter" = "Enter";

// Device Types
"device.type.binoculars" = "AI Binoculars";
"device.type.monocular" = "AI Monocular";

// Gallery
"gallery.media" = "Media";
"gallery.albums" = "Albums";
"gallery.photos" = "Photos";
"gallery.videos" = "Videos";
"gallery.favorites" = "Favorites";
"gallery.all" = "All";
"gallery.recents" = "Recents";
"gallery.photo" = "Photo";
"gallery.video" = "Video";

// Gallery Placeholders
"gallery.media.placeholder" = "Media Library";
"gallery.media.description" = "Your photos and videos will appear here once you grant permission to access your photo library.";
"gallery.media.loading" = "Loading Media Library";
"gallery.media.loading.description" = "Loading your photos and videos...";
"gallery.albums.placeholder" = "Device Albums";
"gallery.albums.description" = "Albums from your connected Solvia devices will appear here.";

// Gallery Sorting
"gallery.sort.date_newest" = "Newest First";
"gallery.sort.date_oldest" = "Oldest First";
"gallery.sort.name_asc" = "Name A-Z";
"gallery.sort.name_desc" = "Name Z-A";

// Gallery Loading and Actions
"gallery.loading" = "Loading...";
"gallery.retry" = "Retry";
"gallery.favorite" = "Add to Favorites";
"gallery.unfavorite" = "Remove from Favorites";
"gallery.share" = "Share";
"gallery.delete" = "Delete";

// Gallery Sorting
"gallery.sort.title" = "Sort by";

// Gallery Empty States
"gallery.empty.photos.title" = "No Photos";
"gallery.empty.photos.description" = "Photos you take will appear here.";
"gallery.empty.videos.title" = "No Videos";
"gallery.empty.videos.description" = "Videos you record will appear here.";
"gallery.empty.favorites.title" = "No Favorites";
"gallery.empty.favorites.description" = "Photos and videos you favorite will appear here.";

// Gallery Errors
"gallery.error.no_permission" = "No photo library access permission";
"gallery.error.loading_failed" = "Failed to load media";

// Common
"common.cancel" = "Cancel";
"common.confirm" = "Confirm";
"common.done" = "Done";
"common.save" = "Save";