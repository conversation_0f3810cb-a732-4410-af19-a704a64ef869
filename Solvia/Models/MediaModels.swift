//
//  MediaModels.swift
//  Solvia
//
//  Created by si<PERSON> chen on 2025/7/28.
//

import Foundation
import Photos
import SwiftUI

// MARK: - Media Item Model
struct MediaItem: Identifiable, Codable {
    let id: String
    let type: MediaType
    let creationDate: Date
    var isFavorite: Bool
    let localIdentifier: String // PHAsset的localIdentifier
    let duration: TimeInterval? // 视频时长，图片为nil
    
    init(id: String = UUID().uuidString, type: MediaType, creationDate: Date, isFavorite: Bool = false, localIdentifier: String, duration: TimeInterval? = nil) {
        self.id = id
        self.type = type
        self.creationDate = creationDate
        self.isFavorite = isFavorite
        self.localIdentifier = localIdentifier
        self.duration = duration
    }
    
    enum MediaType: String, CaseIterable, Codable {
        case photo = "photo"
        case video = "video"
        
        var displayName: String {
            switch self {
            case .photo:
                return "gallery.photo".localized
            case .video:
                return "gallery.video".localized
            }
        }
        
        var systemIconName: String {
            switch self {
            case .photo:
                return "photo"
            case .video:
                return "video"
            }
        }
    }
}

// MARK: - Media Album Model
struct MediaAlbum: Identifiable, Codable {
    let id: String
    let name: String
    let type: AlbumType
    var mediaItems: [MediaItem]
    let creationDate: Date
    
    init(id: String = UUID().uuidString, name: String, type: AlbumType, mediaItems: [MediaItem] = [], creationDate: Date = Date()) {
        self.id = id
        self.name = name
        self.type = type
        self.mediaItems = mediaItems
        self.creationDate = creationDate
    }
    
    enum AlbumType: String, CaseIterable, Codable {
        case all = "all"
        case favorites = "favorites"
        case photos = "photos"
        case videos = "videos"
        case recents = "recents"
        
        var displayName: String {
            switch self {
            case .all:
                return "gallery.all".localized
            case .favorites:
                return "gallery.favorites".localized
            case .photos:
                return "gallery.photos".localized
            case .videos:
                return "gallery.videos".localized
            case .recents:
                return "gallery.recents".localized
            }
        }
    }
    
    var itemCount: Int {
        return mediaItems.count
    }
    
    var latestItem: MediaItem? {
        return mediaItems.sorted { $0.creationDate > $1.creationDate }.first
    }
}

// MARK: - Device Album Model
struct DeviceAlbum: Identifiable, Codable {
    let id: String
    let deviceId: String // 关联的设备ID
    let deviceName: String
    let deviceType: String
    var mediaItems: [DeviceMediaItem]
    let creationDate: Date
    var lastSyncDate: Date
    
    init(id: String = UUID().uuidString, deviceId: String, deviceName: String, deviceType: String, mediaItems: [DeviceMediaItem] = [], creationDate: Date = Date(), lastSyncDate: Date = Date()) {
        self.id = id
        self.deviceId = deviceId
        self.deviceName = deviceName
        self.deviceType = deviceType
        self.mediaItems = mediaItems
        self.creationDate = creationDate
        self.lastSyncDate = lastSyncDate
    }
    
    var itemCount: Int {
        return mediaItems.count
    }
    
    var latestItem: DeviceMediaItem? {
        return mediaItems.sorted { $0.creationDate > $1.creationDate }.first
    }
    
    var photoCount: Int {
        return mediaItems.filter { $0.type == .photo }.count
    }
    
    var videoCount: Int {
        return mediaItems.filter { $0.type == .video }.count
    }
}

// MARK: - Device Media Item Model
struct DeviceMediaItem: Identifiable, Codable {
    let id: String
    let type: MediaType
    let creationDate: Date
    let fileName: String
    let fileSize: Int64 // 文件大小（字节）
    let thumbnailPath: String? // 缩略图路径
    let fullImagePath: String? // 完整图片路径
    var isDownloaded: Bool // 是否已下载到本地
    
    init(id: String = UUID().uuidString, type: MediaType, creationDate: Date, fileName: String, fileSize: Int64, thumbnailPath: String? = nil, fullImagePath: String? = nil, isDownloaded: Bool = false) {
        self.id = id
        self.type = type
        self.creationDate = creationDate
        self.fileName = fileName
        self.fileSize = fileSize
        self.thumbnailPath = thumbnailPath
        self.fullImagePath = fullImagePath
        self.isDownloaded = isDownloaded
    }
    
    enum MediaType: String, CaseIterable, Codable {
        case photo = "photo"
        case video = "video"
        
        var displayName: String {
            switch self {
            case .photo:
                return "gallery.photo".localized
            case .video:
                return "gallery.video".localized
            }
        }
    }
    
    var formattedFileSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
}

// MARK: - Media Section Model (for grouped display)
struct MediaSection: Identifiable {
    let id: String
    let date: Date
    let items: [MediaItem]
    
    init(date: Date, items: [MediaItem]) {
        self.id = UUID().uuidString
        self.date = date
        self.items = items
    }
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }
    
    var itemCount: Int {
        return items.count
    }
}

// MARK: - Sort Options
enum MediaSortOption: String, CaseIterable {
    case dateDescending = "date_desc"
    case dateAscending = "date_asc"
    case nameAscending = "name_asc"
    case nameDescending = "name_desc"
    
    var displayName: String {
        switch self {
        case .dateDescending:
            return "gallery.sort.date_newest".localized
        case .dateAscending:
            return "gallery.sort.date_oldest".localized
        case .nameAscending:
            return "gallery.sort.name_asc".localized
        case .nameDescending:
            return "gallery.sort.name_desc".localized
        }
    }
    
    var systemIconName: String {
        switch self {
        case .dateDescending:
            return "calendar.badge.minus"
        case .dateAscending:
            return "calendar.badge.plus"
        case .nameAscending:
            return "textformat.abc"
        case .nameDescending:
            return "textformat.abc"
        }
    }
}