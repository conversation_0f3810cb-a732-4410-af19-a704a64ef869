//
//  DeviceModel.swift
//  Solvia
//
//  Created by <PERSON><PERSON> chen on 2025/7/25.
//

import Foundation

// MARK: - Device Model
struct SolviaDevice: Identifiable, Codable {
    let id: UUID
    let name: String
    let type: DeviceType
    let ssid: String
    var isConnected: Bool
    let dateAdded: Date
    var hasConnectedBefore: Bool // 标记是否曾经连接过

    init(name: String, type: DeviceType, ssid: String, isConnected: Bool, dateAdded: Date, hasConnectedBefore: Bool = false) {
        self.id = UUID()
        self.name = name
        self.type = type
        self.ssid = ssid
        self.isConnected = isConnected
        self.dateAdded = dateAdded
        self.hasConnectedBefore = hasConnectedBefore
    }
    
    enum DeviceType: String, CaseIterable, Codable {
        case binoculars = "binoculars"
        case monocular = "monocular"
        
        var displayName: String {
            switch self {
            case .binoculars:
                return "device.type.binoculars".localized
            case .monocular:
                return "device.type.monocular".localized
            }
        }
        
        var iconName: String {
            switch self {
            case .binoculars:
                return "ai_binoculars"
            case .monocular:
                return "ai_monocular"
            }
        }
    }
}

// MARK: - Device Manager
class DeviceManager: ObservableObject {
    static let shared = DeviceManager()
    
    @Published var devices: [SolviaDevice] = []
    @Published var currentlyConnectedDevice: SolviaDevice?
    
    private let userDefaults = UserDefaults.standard
    private let devicesKey = "SavedSolviaDevices"
    
    private init() {
        loadDevices()
        loadDeviceAlbums()
        startConnectionMonitoring()
    }
    
    // MARK: - Device Management
    func addDevice(_ device: SolviaDevice) {
        // 检查是否已存在相同SSID的设备
        if let existingIndex = devices.firstIndex(where: { $0.ssid == device.ssid }) {
            // 更新现有设备
            devices[existingIndex] = device
        } else {
            // 添加新设备
            devices.append(device)
        }
        saveDevices()
    }
    
    func removeDevice(_ device: SolviaDevice) {
        devices.removeAll { $0.id == device.id }
        if currentlyConnectedDevice?.id == device.id {
            currentlyConnectedDevice = nil
        }
        saveDevices()
    }
    
    func updateDeviceConnectionStatus(_ device: SolviaDevice, isConnected: Bool) {
        if let index = devices.firstIndex(where: { $0.id == device.id }) {
            devices[index].isConnected = isConnected
            
            if isConnected {
                // 设置为当前连接的设备
                currentlyConnectedDevice = devices[index]
                // 将其他设备设置为未连接
                for i in devices.indices {
                    if devices[i].id != device.id {
                        devices[i].isConnected = false
                    }
                }
            } else if currentlyConnectedDevice?.id == device.id {
                currentlyConnectedDevice = nil
            }
            
            saveDevices()
        }
    }
    
    // MARK: - Connection Monitoring
    private func startConnectionMonitoring() {
        // 每5秒检查一次连接状态
        Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { _ in
            self.checkConnectionStatus()
        }
    }
    
    private func checkConnectionStatus() {
        let wifiManager = WiFiManager.shared
        let currentSSID = wifiManager.getCurrentWiFiSSID()
        
        // 更新所有设备的连接状态
        for i in devices.indices {
            let wasConnected = devices[i].isConnected
            let isNowConnected = currentSSID == devices[i].ssid

            if wasConnected != isNowConnected {
                devices[i].isConnected = isNowConnected

                if isNowConnected {
                    // 标记设备已连接过
                    devices[i].hasConnectedBefore = true
                    currentlyConnectedDevice = devices[i]
                    // 断开其他设备
                    for j in devices.indices {
                        if j != i {
                            devices[j].isConnected = false
                        }
                    }
                } else if currentlyConnectedDevice?.id == devices[i].id {
                    currentlyConnectedDevice = nil
                }
            }
        }
        
        saveDevices()
    }
    
    // MARK: - Persistence
    private func saveDevices() {
        if let encoded = try? JSONEncoder().encode(devices) {
            userDefaults.set(encoded, forKey: devicesKey)
        }
    }
    
    private func loadDevices() {
        if let data = userDefaults.data(forKey: devicesKey),
           let decoded = try? JSONDecoder().decode([SolviaDevice].self, from: data) {
            devices = decoded
            
            // 找到当前连接的设备
            currentlyConnectedDevice = devices.first { $0.isConnected }
        }
    }
    
    // MARK: - Helper Methods
    var hasDevices: Bool {
        return !devices.isEmpty
    }
    
    func getDeviceBySSID(_ ssid: String) -> SolviaDevice? {
        return devices.first { $0.ssid == ssid }
    }
    
    // MARK: - Device Album Management
    @Published var deviceAlbums: [DeviceAlbum] = []
    private let deviceAlbumsKey = "SavedDeviceAlbums"
    
    func getDeviceAlbums() -> [DeviceAlbum] {
        return deviceAlbums
    }
    
    func getDeviceAlbum(for device: SolviaDevice) -> DeviceAlbum? {
        return deviceAlbums.first { $0.deviceId == device.id.uuidString }
    }
    
    func createDeviceAlbum(for device: SolviaDevice) -> DeviceAlbum {
        let album = DeviceAlbum(
            deviceId: device.id.uuidString,
            deviceName: device.name,
            deviceType: device.type.rawValue
        )
        
        if let existingIndex = deviceAlbums.firstIndex(where: { $0.deviceId == device.id.uuidString }) {
            deviceAlbums[existingIndex] = album
        } else {
            deviceAlbums.append(album)
        }
        
        saveDeviceAlbums()
        return album
    }
    
    func updateDeviceAlbum(_ album: DeviceAlbum) {
        if let index = deviceAlbums.firstIndex(where: { $0.id == album.id }) {
            deviceAlbums[index] = album
            saveDeviceAlbums()
        }
    }
    
    func addMediaToDeviceAlbum(deviceId: String, mediaItem: DeviceMediaItem) {
        if let index = deviceAlbums.firstIndex(where: { $0.deviceId == deviceId }) {
            deviceAlbums[index].mediaItems.append(mediaItem)
            deviceAlbums[index].lastSyncDate = Date()
            saveDeviceAlbums()
        }
    }
    
    func removeMediaFromDeviceAlbum(deviceId: String, mediaItemId: String) {
        if let albumIndex = deviceAlbums.firstIndex(where: { $0.deviceId == deviceId }) {
            deviceAlbums[albumIndex].mediaItems.removeAll { $0.id == mediaItemId }
            deviceAlbums[albumIndex].lastSyncDate = Date()
            saveDeviceAlbums()
        }
    }
    
    func getDevicePhotos(for device: SolviaDevice) -> [DeviceMediaItem] {
        guard let album = getDeviceAlbum(for: device) else { return [] }
        return album.mediaItems.filter { $0.type == .photo }
    }
    
    func getDeviceVideos(for device: SolviaDevice) -> [DeviceMediaItem] {
        guard let album = getDeviceAlbum(for: device) else { return [] }
        return album.mediaItems.filter { $0.type == .video }
    }
    
    // 模拟设备相册数据（用于演示）
    func generateMockDeviceAlbumData(for device: SolviaDevice) {
        let mockItems = generateMockDeviceMediaItems()
        let album = DeviceAlbum(
            deviceId: device.id.uuidString,
            deviceName: device.name,
            deviceType: device.type.rawValue,
            mediaItems: mockItems
        )
        
        if let existingIndex = deviceAlbums.firstIndex(where: { $0.deviceId == device.id.uuidString }) {
            deviceAlbums[existingIndex] = album
        } else {
            deviceAlbums.append(album)
        }
        
        saveDeviceAlbums()
    }
    
    private func generateMockDeviceMediaItems() -> [DeviceMediaItem] {
        var items: [DeviceMediaItem] = []
        let calendar = Calendar.current
        
        // 生成一些模拟的设备媒体项目
        for i in 1...15 {
            let date = calendar.date(byAdding: .day, value: -i, to: Date()) ?? Date()
            let isVideo = i % 4 == 0 // 每4个项目中有1个视频
            
            let item = DeviceMediaItem(
                type: isVideo ? .video : .photo,
                creationDate: date,
                fileName: isVideo ? "VID_\(String(format: "%04d", i)).mp4" : "IMG_\(String(format: "%04d", i)).jpg",
                fileSize: Int64.random(in: isVideo ? 10_000_000...50_000_000 : 1_000_000...5_000_000),
                thumbnailPath: "/thumbnails/thumb_\(i).jpg",
                fullImagePath: isVideo ? "/videos/vid_\(i).mp4" : "/images/img_\(i).jpg",
                isDownloaded: Bool.random()
            )
            items.append(item)
        }
        
        return items
    }
    
    private func saveDeviceAlbums() {
        if let encoded = try? JSONEncoder().encode(deviceAlbums) {
            userDefaults.set(encoded, forKey: deviceAlbumsKey)
        }
    }
    
    private func loadDeviceAlbums() {
        if let data = userDefaults.data(forKey: deviceAlbumsKey),
           let decoded = try? JSONDecoder().decode([DeviceAlbum].self, from: data) {
            deviceAlbums = decoded
        }
    }
}